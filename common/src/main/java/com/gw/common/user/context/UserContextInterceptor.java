package com.gw.common.user.context;


import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;

@Component
@Log4j2
public class UserContextInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            log.info("header: {}", request.getHeaderNames());
            UserContext userContext = new UserContext();
            userContext.setUserId(request.getHeader("X-User-Id"));
            userContext.setUsername(request.getHeader("X-User-Name"));
            String encodedRealName = request.getHeader("X-User-RealName");
            if (encodedRealName != null) {
                try {
                    userContext.setRealName(new String(Base64.getDecoder().decode(encodedRealName), StandardCharsets.UTF_8));
                    log.info("Decoded real name: {}", userContext.getRealName());
                } catch (IllegalArgumentException e) {
                    // 如果不是Base64编码，直接使用原始值
                    userContext.setRealName(encodedRealName);
                    log.warn("Failed to decode real name as Base64, using original value: {}", encodedRealName);
                }
            }

            String roles = request.getHeader("X-User-Roles");
            if (StringUtils.hasText(roles)) {
                userContext.setRoles(Arrays.asList(roles.split(",")));
            }

            UserContext.set(userContext);
            return true;
        } catch (Exception e) {
            log.error("Error setting user context", e);
            return true;
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response,
                                Object handler, Exception ex) {
        UserContext.remove(); // 清理ThreadLocal
    }
}
