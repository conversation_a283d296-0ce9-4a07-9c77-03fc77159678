package com.gw.common.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 灵活的LocalDate反序列化器
 * 支持多种日期格式的解析，包括：
 * - yyyy-MM-dd (标准日期格式)
 * - yyyy-MM-dd HH:mm:ss (日期时间格式，提取日期部分)
 * - yyyy-MM-dd'T'HH:mm:ss (ISO日期时间格式，提取日期部分)
 * - yyyy-MM-dd'T'HH:mm:ss.SSS (ISO日期时间格式带毫秒，提取日期部分)
 */
@Log4j2
public class FlexibleLocalDateDeserializer extends JsonDeserializer<LocalDate> {

    // 支持的日期格式列表，按优先级排序
    private static final DateTimeFormatter[] DATE_FORMATTERS = {
            DateTimeFormatter.ofPattern("yyyy-MM-dd"),                    // 标准日期格式
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),          // 日期时间格式
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),        // ISO日期时间格式
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"),    // ISO日期时间格式带毫秒
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS"), // ISO日期时间格式带微秒
            DateTimeFormatter.ISO_LOCAL_DATE,                            // ISO标准日期格式
            DateTimeFormatter.ISO_LOCAL_DATE_TIME                        // ISO标准日期时间格式
    };

    @Override
    public LocalDate deserialize(JsonParser parser, DeserializationContext context) throws IOException {
        String dateString = parser.getText();
        
        if (dateString == null || dateString.trim().isEmpty()) {
            return null;
        }
        
        dateString = dateString.trim();
        
        // 尝试使用各种格式解析日期
        for (DateTimeFormatter formatter : DATE_FORMATTERS) {
            try {
                // 首先尝试直接解析为LocalDate
                if (isDateOnlyFormat(formatter)) {
                    return LocalDate.parse(dateString, formatter);
                } else {
                    // 对于日期时间格式，先解析为LocalDateTime再提取日期部分
                    LocalDateTime dateTime = LocalDateTime.parse(dateString, formatter);
                    return dateTime.toLocalDate();
                }
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
                log.debug("Failed to parse date '{}' with formatter '{}': {}", 
                         dateString, formatter.toString(), e.getMessage());
            }
        }
        
        // 如果所有格式都失败，记录错误并抛出异常
        log.error("Unable to parse date string '{}' with any supported format", dateString);
        throw new IOException(String.format(
            "Unable to parse date string '%s'. Supported formats: yyyy-MM-dd, yyyy-MM-dd HH:mm:ss, yyyy-MM-dd'T'HH:mm:ss, etc.",
            dateString
        ));
    }
    
    /**
     * 判断格式器是否为纯日期格式（不包含时间信息）
     */
    private boolean isDateOnlyFormat(DateTimeFormatter formatter) {
        String pattern = formatter.toString();
        return pattern.contains("yyyy-MM-dd") && 
               !pattern.contains("HH") && 
               !pattern.contains("mm") && 
               !pattern.contains("ss");
    }
}
