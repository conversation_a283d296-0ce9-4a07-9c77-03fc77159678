package com.gw.common.util;

public class StringUtils {
    public static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        for (int i = 0; i < str.length(); i++) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    public static boolean isEmpty(String str) {
        return str == null || str.isEmpty();
    }

    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    public static boolean hasText(String text) {
        return (text != null && !text.trim().isEmpty());
    }

    public static String removeParenthesisContent(String input) {
        // Using recursive approach to handle nested parentheses
        String regex = "(\\([^()（）]*\\))|(（[^()（）]*）)";
        String result = input;
        String previous;

        do {
            previous = result;
            result = result.replaceAll(regex, "");
        } while (!previous.equals(result));

        return result;
    }
}
