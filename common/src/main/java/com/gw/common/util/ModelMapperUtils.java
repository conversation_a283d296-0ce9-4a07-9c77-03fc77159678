package com.gw.common.util;

import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * ModelMapper工具类
 * 提供ModelMapper的最佳实践配置和常用方法
 */
public class ModelMapperUtils {

    private static final Logger log = LoggerFactory.getLogger(ModelMapperUtils.class);

    /**
     * ModelMapper实例缓存，避免重复创建
     */
    private static final ConcurrentHashMap<String, ModelMapper> MAPPER_CACHE = new ConcurrentHashMap<>();

    /**
     * 默认的ModelMapper实例
     */
    private static final ModelMapper DEFAULT_MAPPER = createDefaultMapper();

    /**
     * 创建默认配置的ModelMapper
     */
    private static ModelMapper createDefaultMapper() {
        ModelMapper mapper = new ModelMapper();
        mapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT)
                .setFieldMatchingEnabled(true)
                .setFieldAccessLevel(org.modelmapper.config.Configuration.AccessLevel.PRIVATE);
        return mapper;
    }

    /**
     * 获取默认的ModelMapper实例
     *
     * @return ModelMapper实例
     */
    public static ModelMapper getDefaultMapper() {
        return DEFAULT_MAPPER;
    }

    /**
     * 创建并缓存自定义配置的ModelMapper
     *
     * @param key          缓存键
     * @param configurator 配置函数
     * @return ModelMapper实例
     */
    public static ModelMapper getOrCreateMapper(String key, Consumer<ModelMapper> configurator) {
        return MAPPER_CACHE.computeIfAbsent(key, k -> {
            ModelMapper mapper = new ModelMapper();
            // 应用默认配置
            mapper.getConfiguration()
                    .setMatchingStrategy(MatchingStrategies.STRICT)
                    .setFieldMatchingEnabled(true)
                    .setFieldAccessLevel(org.modelmapper.config.Configuration.AccessLevel.PRIVATE);

            // 应用自定义配置
            if (configurator != null) {
                configurator.accept(mapper);
            }

            log.debug("Created and cached ModelMapper with key: {}", key);
            return mapper;
        });
    }

    /**
     * 安全的对象映射方法
     *
     * @param source          源对象
     * @param destinationType 目标类型
     * @param <T>             目标类型泛型
     * @return 映射后的对象，如果源对象为null则返回null
     */
    public static <T> T mapSafely(Object source, Class<T> destinationType) {
        if (source == null) {
            return null;
        }
        try {
            return DEFAULT_MAPPER.map(source, destinationType);
        } catch (Exception e) {
            log.error("Failed to map {} to {}: {}",
                    source.getClass().getSimpleName(),
                    destinationType.getSimpleName(),
                    e.getMessage());
            throw new RuntimeException("Object mapping failed", e);
        }
    }

    /**
     * 使用指定的ModelMapper进行安全映射
     *
     * @param source          源对象
     * @param destinationType 目标类型
     * @param mapper          指定的ModelMapper
     * @param <T>             目标类型泛型
     * @return 映射后的对象
     */
    public static <T> T mapSafely(Object source, Class<T> destinationType, ModelMapper mapper) {
        if (source == null) {
            return null;
        }
        try {
            return mapper.map(source, destinationType);
        } catch (Exception e) {
            log.error("Failed to map {} to {} using custom mapper: {}",
                    source.getClass().getSimpleName(),
                    destinationType.getSimpleName(),
                    e.getMessage());
            throw new RuntimeException("Object mapping failed", e);
        }
    }

    /**
     * 映射到现有对象实例
     *
     * @param source      源对象
     * @param destination 目标对象
     */
    public static void mapToExisting(Object source, Object destination) {
        if (source == null || destination == null) {
            return;
        }
        try {
            DEFAULT_MAPPER.map(source, destination);
        } catch (Exception e) {
            log.error("Failed to map {} to existing {}: {}",
                    source.getClass().getSimpleName(),
                    destination.getClass().getSimpleName(),
                    e.getMessage());
            throw new RuntimeException("Object mapping failed", e);
        }
    }

    /**
     * 清除ModelMapper缓存
     */
    public static void clearCache() {
        MAPPER_CACHE.clear();
        log.info("ModelMapper cache cleared");
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存大小
     */
    public static int getCacheSize() {
        return MAPPER_CACHE.size();
    }

    /**
     * 验证映射配置是否正确
     *
     * @param mapper          ModelMapper实例
     * @param sourceType      源类型
     * @param destinationType 目标类型
     * @return 验证是否通过
     */
    public static boolean validateMapping(ModelMapper mapper, Class<?> sourceType, Class<?> destinationType) {
        try {
            mapper.validate();
            log.debug("Mapping validation passed for {} -> {}",
                    sourceType.getSimpleName(),
                    destinationType.getSimpleName());
            return true;
        } catch (Exception e) {
            log.warn("Mapping validation failed for {} -> {}: {}",
                    sourceType.getSimpleName(),
                    destinationType.getSimpleName(),
                    e.getMessage());
            return false;
        }
    }
}