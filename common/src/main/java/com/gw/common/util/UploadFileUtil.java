package com.gw.common.util;

import com.alibaba.fastjson2.JSON;
import lombok.extern.log4j.Log4j2;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.List;
import java.util.UUID;

@Log4j2
public class UploadFileUtil {

    private static final String FILE_SEPARATOR = "/";

    /**
     * Check if a file exists at the given URL path
     */
    public static boolean checkFileExistence(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            return false;
        }
        try {
            java.nio.file.Path path = java.nio.file.Paths.get(filePath);
            return java.nio.file.Files.exists(path);
        } catch (Exception e) {
            log.warn("Failed to check file existence: {}", e.getMessage());
            return false;
        }
    }

    public static String handleFileUpload(MultipartFile file, List<String> allowExtension,
                                          int maxSizeMB, String uploadDir, String path) {
        // 参数校验
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("上传文件不能为空");
        }
        if (allowExtension == null || allowExtension.isEmpty()) {
            throw new IllegalArgumentException("允许的文件类型不能为空");
        }

        // 获取文件信息
        String originalFileName = file.getOriginalFilename();
        if (originalFileName == null) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        // 验证文件类型
        String fileExtension = getFileExtension(originalFileName);
        validateFileExtension(fileExtension, allowExtension);

        // 验证文件大小
        validateFileSize(file.getSize(), maxSizeMB);

        // 生成新文件名
        String newFileName = generateFileName(fileExtension);

        // 构建存储路径
        Path uploadPath = buildUploadPath(uploadDir, path);
        Path filePath = uploadPath.resolve(newFileName);

        // 保存文件
        saveFile(file, filePath);

        // 返回相对路径
        return path + FILE_SEPARATOR + newFileName;
    }

    private static String getFileExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf(".");
        return (dotIndex > 0) ? fileName.substring(dotIndex + 1).toLowerCase() : "";
    }

    private static void validateFileExtension(String fileExtension, List<String> allowExtension) {
        if (!allowExtension.contains(fileExtension.toLowerCase())) {
            throw new IllegalArgumentException("不支持的文件类型,当前支持:" + JSON.toJSONString(allowExtension));
        }
    }

    private static void validateFileSize(long fileSize, int maxSizeMB) {
        long maxSizeBytes = maxSizeMB * 1024L * 1024L;
        if (fileSize > maxSizeBytes) {
            throw new IllegalArgumentException("文件大小超过限制:" + maxSizeMB + "MB");
        }
    }

    private static String generateFileName(String fileExtension) {
        return UUID.randomUUID() + "." + fileExtension;
    }

    public static Path buildUploadPath(String uploadDir, String path) {
        Path uploadPath;
        if (!StringUtils.hasLength(uploadDir)) {
            uploadPath = Paths.get("").toAbsolutePath().resolve(path);
        } else {
            uploadPath = Paths.get(uploadDir).resolve(path);
        }

        try {
            Files.createDirectories(uploadPath);
        } catch (IOException e) {
            log.error("创建目录失败: {}", uploadPath, e);
            throw new RuntimeException("创建上传目录失败");
        }

        return uploadPath;
    }

    private static void saveFile(MultipartFile file, Path filePath) {
        try {
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            log.error("保存文件失败: {}", filePath, e);
            throw new RuntimeException("保存文件失败");
        }
    }

    /**
     * 删除指定路径的文件
     * @param filePath 文件路径（相对路径或绝对路径）
     * @return 删除是否成功
     */
    public static boolean deleteFile(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            log.warn("文件路径为空，无法删除");
            return false;
        }

        try {
            Path path = Paths.get(filePath);
            if (Files.exists(path)) {
                Files.delete(path);
                log.info("成功删除文件: {}", filePath);
                return true;
            } else {
                log.warn("文件不存在，无法删除: {}", filePath);
                return false;
            }
        } catch (IOException e) {
            log.error("删除文件失败: {}", filePath, e);
            return false;
        } catch (Exception e) {
            log.error("删除文件时发生未知异常: {}", filePath, e);
            return false;
        }
    }

}
