package com.gw.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import lombok.Data;

@Data
@Schema(description = "page的请求")
public class PageBaseRequest<T> {
    @Min(value = 1, message = "current不能小于1")
    @Schema(defaultValue = "1", description = "分页索引,表示第几页，从1开始")
    private int current;
    @Min(value = 1, message = "pageSize不能小于1")
    @Schema(defaultValue = "10", description = "每页的大小")
    private int pageSize;
    private PageBaseOptions option;
    private T filter;
}
