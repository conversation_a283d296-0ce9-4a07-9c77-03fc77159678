package com.gw.common.agent.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Schema(description = "智能体信息")
@NoArgsConstructor
@AllArgsConstructor
public class AgentBaseVO {
    @JsonProperty("i")
    private Long id;
    @Schema(description = "智能体名称")
    @JsonProperty("n")
    private String name;
    @JsonProperty("st")
    private Integer status;
    @Schema(description = "是否公开：1-私密，2-公开")
    @JsonProperty("pu")
    private Integer isPublic;
    @JsonProperty("btu")
    private String bgThumbnailUrl;
    @JsonProperty("bd")
    private String remoteBotId;
    @JsonProperty("c")
    private String creator;
    @JsonProperty("ss")
    private Integer shelfStatus;
    @JsonProperty("pr")
    private AgentProfileBaseVO profile;
    @JsonProperty("idt")
    private String identity;
    @JsonProperty("gs")
    private Integer gender;
    @JsonProperty("plm")
    private Integer platform = 1;
    @JsonProperty("mdl")
    private String model = "";
    @JsonProperty("ts")
    private List<AgentTagVO> tags;
    @JsonProperty("tyi")
    private Long typeId;
    @JsonProperty("ty")
    private AgentTypeVO type;
    @JsonProperty("itr")
    private String introduction;
}
