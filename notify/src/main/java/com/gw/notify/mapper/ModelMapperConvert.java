package com.gw.notify.mapper;

import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;

public class ModelMapperConvert {
    private static final ModelMapper NOTIFY_MODEL_MAPPER = createNotifyModelMapper();

    private static ModelMapper createNotifyModelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT);
        return modelMapper;
    }

    public static ModelMapper getNotifyModelMapper() {
        return NOTIFY_MODEL_MAPPER;
    }

    public static <T> T convert(Object source, Class<T> targetClass) {
        return NOTIFY_MODEL_MAPPER.map(source, targetClass);
    }
}