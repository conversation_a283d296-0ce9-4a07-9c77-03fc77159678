package com.gw.notify.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.notify.entity.SystemMessageUserEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Optional;

/**
 * 系统消息用户关联表 Mapper 接口
 */
@Mapper
public interface SystemMessageUserMapper extends BaseMapper<SystemMessageUserEntity> {

    /**
     * 将指定用户的某条消息标记为已读
     *
     * @param messageId 消息ID
     * @param username  用户名
     * @return 影响行数
     */
    @Update("UPDATE t_system_message_user SET status = 2 WHERE message_id = #{messageId} AND username = #{username} AND status = 1")
    int markAsRead(@Param("messageId") Long messageId, @Param("username") String username);

    @Select("SELECT * FROM t_system_message_user WHERE message_id = #{messageId} AND username = #{username} Limit 1")
    Optional<SystemMessageUserEntity> findByMessageIdAndUsername(@Param("messageId") Long messageId, @Param("username") String username);

    /**
     * 根据消息ID删除所有用户关联记录
     *
     * @param messageId 消息ID
     * @return 影响行数
     */
    @Delete("DELETE FROM t_system_message_user WHERE message_id = #{messageId}")
    // Assuming BaseEntity has deleted flag
    int deleteByMessageId(@Param("messageId") Long messageId);

    @Select("SELECT * FROM t_system_message_user WHERE username = #{username} ORDER BY create_time DESC")
    List<SystemMessageUserEntity> pageByUsernameOrderByCreateTimeDesc(@Param("username") String username);

    @Update("UPDATE t_system_message_user SET status = 2 WHERE username = #{username}")
    void markAllAsReadByUsername(@Param("username") String username);

} 