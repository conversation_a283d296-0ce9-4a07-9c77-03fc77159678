package com.gw.notify.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.notify.dto.InteractiveMessageQueryDTO;
import com.gw.notify.entity.InteractiveMessageEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Optional;

/**
 * 互动消息数据访问接口
 */
public interface InteractiveMessageMapper extends BaseMapper<InteractiveMessageEntity> {
    /**
     * 根据ID查询消息
     *
     * @param id 消息ID
     * @return 消息实体
     */
    @Select("SELECT * FROM t_active_message WHERE id = #{id} AND deleted = 0")
    Optional<InteractiveMessageEntity> findById(@Param("id") Long id);

    /**
     * 查询关联特定智能体的互动消息
     *
     * @param agentId 智能体ID
     * @return 互动消息列表
     */
    @Select("SELECT * FROM t_active_message WHERE associated_with_agent = 1 AND agent_id = #{agentId} AND deleted = 0 ORDER BY create_time DESC")
    List<InteractiveMessageEntity> findMessagesByAgentId(@Param("agentId") Long agentId);

    /**
     * 根据ID查询消息
     *
     * @param id 消息ID
     * @return 消息实体
     */
    @Select("SELECT * FROM t_active_message WHERE id = #{id} AND deleted = 0")
    InteractiveMessageEntity findValidById(@Param("id") Long id);

    @Select("SELECT * FROM t_active_message WHERE id IN (#{ids}) AND deleted = 0")
    List<InteractiveMessageEntity> findAllByIds(@Param("ids") List<Long> ids);

    List<InteractiveMessageEntity> pageByUsername(InteractiveMessageQueryDTO query);

    List<InteractiveMessageEntity> page(InteractiveMessageQueryDTO query);
} 