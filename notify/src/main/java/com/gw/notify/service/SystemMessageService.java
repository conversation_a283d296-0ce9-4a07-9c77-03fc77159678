package com.gw.notify.service;

import com.github.pagehelper.PageInfo;
import com.gw.notify.dto.SystemMessageQueryDTO;
import com.gw.notify.entity.SystemMessageEntity;

/**
 * 系统消息服务接口
 */
public interface SystemMessageService {


    void createMessage(SystemMessageEntity entity);

    void updateMessage(SystemMessageEntity entity);

    void deleteMessage(SystemMessageEntity entity);

    SystemMessageEntity findById(Long id);

    PageInfo<SystemMessageEntity> pageValidMessagesByUsername(int pageNum, int pageSize, SystemMessageQueryDTO query);

    PageInfo<SystemMessageEntity> pageMessages(int pageNum, int pageSize, SystemMessageQueryDTO query);

    PageInfo<SystemMessageEntity> pagePublic(int pageNum, int pageSize, SystemMessageQueryDTO query);
}