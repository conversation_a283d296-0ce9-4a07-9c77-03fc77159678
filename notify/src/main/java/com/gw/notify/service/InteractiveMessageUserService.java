package com.gw.notify.service;

import com.github.pagehelper.PageInfo;
import com.gw.notify.entity.InteractiveMessageUserEntity;

import java.util.List;

/**
 * 互动消息用户关联服务接口
 */
public interface InteractiveMessageUserService {
    void markAsRead(Long messageId, String username);

    void markAllAsReadByUsername(String username);

    boolean existsByMessageIdAndUsername(Long messageId, String username);

    void deleteByMessageId(Long messageId);

    void insertBatch(List<InteractiveMessageUserEntity> entityList);

    PageInfo<InteractiveMessageUserEntity> pageByUsername(int pageNum, int pageSize, String username);
} 