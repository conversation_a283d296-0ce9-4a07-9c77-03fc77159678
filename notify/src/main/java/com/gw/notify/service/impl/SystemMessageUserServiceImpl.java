package com.gw.notify.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gw.notify.entity.SystemMessageUserEntity;
import com.gw.notify.mapper.SystemMessageUserMapper;
import com.gw.notify.service.SystemMessageUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Log4j2
@RequiredArgsConstructor
public class SystemMessageUserServiceImpl extends ServiceImpl<SystemMessageUserMapper, SystemMessageUserEntity> implements SystemMessageUserService {
    @Override
    public void markAsRead(Long messageId, String username) {
        baseMapper.markAsRead(messageId, username);
    }

    @Override
    public void markAllAsReadByUsername(String username) {
        this.baseMapper.markAllAsReadByUsername(username);
    }

    @Override
    public boolean existsByMessageIdAndUsername(Long messageId, String username) {
        return this.baseMapper.findByMessageIdAndUsername(messageId, username).isPresent();
    }

    @Override
    public void deleteByMessageId(Long messageId) {
        baseMapper.deleteByMessageId(messageId);
    }

    @Override
    public void insertBatch(List<SystemMessageUserEntity> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            return;
        }
        this.saveBatch(entityList, entityList.size());
    }


    @Override
    public PageInfo<SystemMessageUserEntity> pageByUsername(int pageNum, int pageSize, String username) {
        PageHelper.startPage(pageNum, pageSize);
        List<SystemMessageUserEntity> list = this.baseMapper.pageByUsernameOrderByCreateTimeDesc(username);
        return new PageInfo<>(list);
    }
}
