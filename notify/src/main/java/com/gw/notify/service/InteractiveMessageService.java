package com.gw.notify.service;

import com.github.pagehelper.PageInfo;
import com.gw.notify.dto.InteractiveMessageQueryDTO;
import com.gw.notify.entity.InteractiveMessageEntity;

/**
 * 互动消息服务接口
 */
public interface InteractiveMessageService {

    void createMessage(InteractiveMessageEntity entity);

    void updateMessage(InteractiveMessageEntity entity);

    void deleteMessage(InteractiveMessageEntity entity);

    InteractiveMessageEntity findById(Long id);

    PageInfo<InteractiveMessageEntity> pageValidMessagesByUsername(int pageNum, int pageSize, InteractiveMessageQueryDTO query);

    PageInfo<InteractiveMessageEntity> pageMessages(int pageNum, int pageSize, InteractiveMessageQueryDTO query);
} 