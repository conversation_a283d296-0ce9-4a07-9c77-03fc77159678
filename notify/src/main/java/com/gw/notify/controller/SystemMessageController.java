package com.gw.notify.controller;

import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageInfo;
import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.PageBaseRequest;
import com.gw.common.dto.ResponseResult;
import com.gw.common.exception.BusinessException;
import com.gw.common.notify.dto.SystemMessageCreateDTO;
import com.gw.common.notify.dto.SystemNotifySubmitDTO;
import com.gw.common.user.constant.UserCommonCacheConstant;
import com.gw.common.user.context.UserContextUtil;
import com.gw.common.user.service.UserProxyService;
import com.gw.common.user.vo.UserBaseContentVo;
import com.gw.common.vo.PageBaseContentVo;
import com.gw.common.vo.PaginationVo;
import com.gw.notify.config.CacheProperties;
import com.gw.notify.dto.SystemMessageQueryDTO;
import com.gw.notify.dto.SystemMessageUpdateDTO;
import com.gw.notify.entity.SystemMessageEntity;
import com.gw.notify.entity.SystemMessageUserEntity;
import com.gw.notify.mapper.ModelMapperConvert;
import com.gw.notify.service.SystemMessageService;
import com.gw.notify.service.SystemMessageUserService;
import com.gw.notify.vo.SystemMessageDetailVO;
import com.gw.notify.vo.SystemMessageVO;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.modelmapper.ModelMapper;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.gw.common.exception.BusinessExceptionCode.FAIL_CODE;

/**
 * 系统消息控制器
 */
@RestController
@RequestMapping("/api/v1/notify/system_message")
@RequiredArgsConstructor
@Tag(name = "系统消息管理", description = "系统消息相关API")
@Log4j2
public class SystemMessageController {

    private final SystemMessageService systemMessageService;
    private final SystemMessageUserService systemMessageUserService;
    private final UserProxyService userProxyService;
    private final CacheProperties cacheProperties;

    /**
     * 填充系统消息信息
     */
    private void fillSystemMessage(SystemMessageCreateDTO req, SystemMessageEntity entity) {
        if (req == null || entity == null) {
            throw new IllegalArgumentException("Request or entity cannot be null");
        }
        if (req.getValidStartDate() == null) {
            req.setValidStartDate(LocalDate.now());
        }
        if (req.getValidEndDate() == null) {
            req.setValidEndDate(LocalDate.MAX);
        }
        String username = UserContextUtil.getCurrentUsername();
        ModelMapper modelMapper = ModelMapperConvert.getNotifyModelMapper();
        entity.setUsernamesFromList(req.getTargetUsernames());
        // 基本字段映射
        modelMapper.map(req, entity);

        // 检查智能体关联
        if (req.getAssociatedWithAgent() == 1 && req.getAgentId() == null) {
            throw new BusinessException(FAIL_CODE.getCode(), "关联智能体时，智能体ID不能为空");
        }

        // 检查有效期
        if (req.getValidStartDate().isAfter(req.getValidEndDate())) {
            throw new BusinessException(FAIL_CODE.getCode(), "开始时间不能晚于结束时间");
        }
        List<SystemMessageUserEntity> users = new ArrayList<>();
        Map<String, UserBaseContentVo> userMap = userProxyService.findAllUserMap(cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY));
        if (req.getTargetUsernames() == null || req.getTargetUsernames().isEmpty()) {
            for (UserBaseContentVo userBaseContentVo : userMap.values()) {
                SystemMessageUserEntity userEntity = new SystemMessageUserEntity();
                userEntity.setMessageId(entity.getId());
                userEntity.setUsername(userBaseContentVo.getUsername());
                userEntity.setStatus(1);
                users.add(userEntity);
            }
        } else {
            for (String username1 : req.getTargetUsernames()) {
                if (userMap.get(username1) == null) {
                    throw new BusinessException(FAIL_CODE.getCode(), username1 + " 用户不存在");
                }
                SystemMessageUserEntity userEntity = new SystemMessageUserEntity();
                userEntity.setMessageId(entity.getId());
                userEntity.setUsername(username1);
                userEntity.setStatus(1);
                users.add(userEntity);
            }
        }
        entity.setUsers(users);
        // 设置更新人和更新时间
        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 创建系统消息
     */
    @Operation(summary = "创建系统消息", description = "创建一条新的系统消息")
    @PostMapping("")
    public ResponseResult<?> createSystemMessage(@RequestBody @Valid SystemMessageCreateDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        SystemMessageEntity entity = new SystemMessageEntity();
        fillSystemMessage(req, entity);
        entity.setCreator(username);
        if(req.getTargetUsernames() ==  null || req.getTargetUsernames().isEmpty()) {
            entity.setCreatType(2);
        }else{
            entity.setCreatType(3);
        }
        systemMessageService.createMessage(entity);
        return ResponseResult.success(null);
    }

    @Operation(summary = "服务自动创建系统消息", description = "服务创建一条新的系统消息")
    @PostMapping("auto_create")
    @Hidden
    public ResponseResult<?> autoCreateSystemMessage(@RequestBody @Valid SystemNotifySubmitDTO req) {
        SystemMessageEntity entity = new SystemMessageEntity();
        fillSystemMessage(req, entity);
        entity.setCreatType(1);
        entity.setCreator(req.getUsername());
        systemMessageService.createMessage(entity);
        return ResponseResult.success(null);
    }

    /**
     * 更新系统消息
     */
    @Operation(summary = "更新系统消息", description = "更新系统消息信息")
    @PostMapping("update")
    public ResponseResult<?> updateSystemMessage(@RequestBody @Valid SystemMessageUpdateDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        SystemMessageEntity entity = systemMessageService.findById(req.getId());

        if (!entity.getCreator().equals(username)) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "无权限操作");
        }
        fillSystemMessage(req, entity);
        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setTitle(req.getTitle());
        if(req.getTargetUsernames() ==  null || req.getTargetUsernames().isEmpty()) {
            entity.setCreatType(2);
        }else{
            entity.setCreatType(3);
        }
        systemMessageService.updateMessage(entity);
        log.info("更新系统消息 {}",req);
        return ResponseResult.success(null);
    }

    /**
     * 标记消息为已读
     */
    @Operation(summary = "标记消息为已读", description = "将系统消息标记为已读")
    @PostMapping("my_mark_read")
    public ResponseResult<?> markMessageAsRead(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();

        systemMessageUserService.markAsRead(req.getId(), username);
        return ResponseResult.success(null);
    }

    /**
     * 标记消息为已读
     */
    @Operation(summary = "标记我的所有消息为已读", description = "标记我的所有消息为已读")
    @PostMapping("my_mark_all_read")
    public ResponseResult<?> markAllMessageAsRead(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        systemMessageUserService.markAllAsReadByUsername(username);
        return ResponseResult.success(null);
    }

    /**
     * 删除系统消息
     */
    @Operation(summary = "删除系统消息", description = "删除指定的系统消息")
    @PostMapping("delete")
    public ResponseResult<?> deleteSystemMessage(@RequestBody @Valid ItemIdDTO req) {
        log.info("删除系统消息 {}",req.getId());
        String username = UserContextUtil.getCurrentUsername();
        SystemMessageEntity entity = systemMessageService.findById(req.getId());
        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
        systemMessageService.deleteMessage(entity);
        log.info("删除系统消息 {}",req.getId());
        return ResponseResult.success(null);
    }

    /**
     * 获取系统消息详情
     */
    @Operation(summary = "获取我的系统消息详情", description = "获取我的系统消息详情")
    @PostMapping("get")
    public ResponseResult<SystemMessageVO> getSystemMessageDetail(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();

        SystemMessageEntity entity = systemMessageService.findById(req.getId());
        if(entity.getTargetUsers() != null &&
                !entity.getTargetUsers().isEmpty() && !entity.getTargetUsers().contains(username)){
            return ResponseResult.failure(FAIL_CODE.getCode(), "无权限查看");
        }

        return ResponseResult.success(convertToVO(entity));
    }
    @Operation(summary = "公共获取系统消息详情", description = "公共获取系统消息详情")
    @PostMapping("public/get")
    public ResponseResult<SystemMessageVO> getSystemMessagePublicDetail(@RequestBody @Valid ItemIdDTO req) {

        SystemMessageEntity entity = systemMessageService.findById(req.getId());
        if(entity.getTargetUsers() != null && !entity.getTargetUsers().isEmpty()){
            return ResponseResult.failure(FAIL_CODE.getCode(), "无权限查看");
        }
        return ResponseResult.success(convertToVO(entity));
    }
    /**
     * 获取当前用户的有效系统消息
     */
    @Operation(summary = "获取当前用户的有效系统消息", description = "获取当前登录用户的有效系统消息")
    @PostMapping("/my_valid")
    public ResponseResult<PageBaseContentVo<SystemMessageVO>> getMyValidMessages(@RequestBody PageBaseRequest<SystemMessageQueryDTO> params) {
        String username = UserContextUtil.getCurrentUsername();
        SystemMessageQueryDTO filter = params.getFilter();
        if (filter == null) {
            filter = new SystemMessageQueryDTO();
        }
        filter.setUsername(username);
        log.info("getMyValidMessages filter:{}", JSON.toJSONString(filter));
        PageInfo<SystemMessageEntity> page = systemMessageService.pageValidMessagesByUsername(
                params.getCurrent(),
                params.getPageSize(), filter);
        List<SystemMessageEntity> entities = page.getList();
        if (entities.isEmpty()) {
            return ResponseResult.success(new PageBaseContentVo<>(Collections.emptyList(), new PaginationVo(page.getTotal(), page.getPageNum(), page.getPageSize())));
        }

        List<SystemMessageVO> vos = entities.stream()
                .map(this::convertToVO)
                .toList();
        return ResponseResult.success(new PageBaseContentVo<>(vos, new PaginationVo(page.getTotal(), page.getPageNum(), page.getPageSize())));
    }

    @Operation(summary = "分页获取系统消息", description = "分页获取系统消息")
    @PostMapping("/public/page")
    public ResponseResult<PageBaseContentVo<SystemMessageDetailVO>> pagePublicSystemMessages(
            @RequestBody @Valid PageBaseRequest<SystemMessageQueryDTO> params) {
        SystemMessageQueryDTO filter = params.getFilter();
        if (filter == null) {
            filter = new SystemMessageQueryDTO();
        }
        PageInfo<SystemMessageEntity> page = systemMessageService.pagePublic(
                params.getCurrent(),
                params.getPageSize(), filter);

        List<SystemMessageEntity> entities = page.getList();

        if (entities.isEmpty()) {
            PaginationVo pagination = new PaginationVo(page.getTotal(), page.getPageNum(), page.getPageSize());
            return ResponseResult.success(new PageBaseContentVo<>(Collections.emptyList(), pagination));
        }

        List<SystemMessageDetailVO> vos = entities.stream()
                .map(this::convertToDetailVO)
                .toList();

        PaginationVo pagination = new PaginationVo(
                page.getTotal(),
                page.getPageNum(),
                page.getPageSize());

        return ResponseResult.success(new PageBaseContentVo<>(vos, pagination));
    }

    /**
     * 分页获取系统消息
     */
    @Operation(summary = "后台分页获取系统消息", description = "后台分页获取系统消息")
    @PostMapping("/admin/page")
    public ResponseResult<PageBaseContentVo<SystemMessageDetailVO>> adminPageSystemMessages(
            @RequestBody @Valid PageBaseRequest<SystemMessageQueryDTO> params) {
        SystemMessageQueryDTO filter = params.getFilter();
        if (filter == null) {
            filter = new SystemMessageQueryDTO();
        }
        filter.setCreatType(2);
        PageInfo<SystemMessageEntity> page = systemMessageService.pageMessages(
                params.getCurrent(),
                params.getPageSize(), filter);

        List<SystemMessageEntity> entities = page.getList();

        if (entities.isEmpty()) {
            PaginationVo pagination = new PaginationVo(page.getTotal(), page.getPageNum(), page.getPageSize());
            return ResponseResult.success(new PageBaseContentVo<>(Collections.emptyList(), pagination));
        }

        List<SystemMessageDetailVO> vos = entities.stream()
                .map(this::convertToDetailVO)
                .toList();

        PaginationVo pagination = new PaginationVo(
                page.getTotal(),
                page.getPageNum(),
                page.getPageSize());

        return ResponseResult.success(new PageBaseContentVo<>(vos, pagination));
    }

    /**
     * 实体转VO
     */
    private SystemMessageVO convertToVO(SystemMessageEntity entity) {
        if (entity == null) {
            return null;
        }

        ModelMapper modelMapper = ModelMapperConvert.getNotifyModelMapper();

        return modelMapper.map(entity, SystemMessageVO.class);
    }

    private SystemMessageDetailVO convertToDetailVO(SystemMessageEntity entity) {
        if (entity == null) {
            return null;
        }

        ModelMapper modelMapper = ModelMapperConvert.getNotifyModelMapper();
        SystemMessageDetailVO vo = modelMapper.map(entity, SystemMessageDetailVO.class);
        vo.setTargetUsernames(entity.getUsernamesToList());
        return vo;
    }
} 