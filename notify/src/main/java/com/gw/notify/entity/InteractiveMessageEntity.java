package com.gw.notify.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gw.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 互动消息实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_active_message", autoResultMap = true)
public class InteractiveMessageEntity extends BaseEntity {

    /**
     * 消息内容
     */
    private String content;
    /**
     * 消息类型：1-智能体收藏，2-智能提点赞 3 智能体评论 4 智能体使用，8 会话评论 9 会话点赞
     */
    private Integer type;
    private String sessionId;
    /**
     * 消息等级：1-普通消息，2-重要消息，3-紧急消息
     */
    private Integer level;

    /**
     * 是否关联智能体：0-否，1-是
     */
    private Integer associatedWithAgent;

    /**
     * 关联的智能体ID
     */
    private Long agentId;

    /**
     * 消息有效期开始时间
     */
    private LocalDate validStartDate;

    /**
     * 消息有效期结束时间
     */
    private LocalDate validEndDate;

    @TableField(exist = false)
    private List<InteractiveMessageUserEntity> users;

    @TableField(exist = false)
    private Integer status;

    @TableField(value = "target_users")
    private String targetUsers;

    public void setUsernamesFromList(List<String> usernames) {
        if (usernames == null) {
            this.targetUsers = "";
            return;
        }
        this.targetUsers = String.join(",", usernames);
    }

    public List<String> getUsernamesToList() {
        if (targetUsers == null) {
            return new ArrayList<>();
        }
        return List.of(targetUsers.split(","));
    }
}
