package com.gw.notify.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gw.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 系统消息实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_system_message", autoResultMap = true)
public class SystemMessageEntity extends BaseEntity {

    private String title = "";
    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息等级：1-普通消息，2-重要消息，3-紧急消息
     */
    private Integer level;

    /**
     * 是否关联智能体：0-否，1-是
     */
    private Integer associatedWithAgent;

    /**
     * 关联的智能体ID
     */
    private Long agentId;

    /**
     * 消息有效期开始时间
     */
    private LocalDate validStartDate;

    /**
     * 消息有效期结束时间
     */
    private LocalDate validEndDate;
    @TableField(exist = false)
    private List<SystemMessageUserEntity> users;
    @TableField(exist = false)
    private Integer status;
    @TableField(value = "target_users")
    private String targetUsers;
    //创建方式：1-自动创建 2-手动创建针对所有人的， 3 手动创建针针对指定用户的
    @TableField(value = "creat_type")
    private Integer creatType = 1;

    public void setUsernamesFromList(List<String> usernames) {
        if (usernames == null) {
            this.targetUsers = "";
            return;
        }
        this.targetUsers = String.join(",", usernames);
    }

    public List<String> getUsernamesToList() {
        if (targetUsers == null || targetUsers.trim().isEmpty()) {
            return new ArrayList<>();
        }
        String[] usernames = targetUsers.split(",");
        List<String> usernameList = new ArrayList<>();
        for (String username : usernames) {
            if (username != null && !username.trim().isEmpty()) {
                usernameList.add(username.trim());
            }
        }
        return usernameList;
    }
} 