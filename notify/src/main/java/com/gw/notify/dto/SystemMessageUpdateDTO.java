package com.gw.notify.dto;

import com.gw.common.notify.dto.SystemMessageCreateDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 更新系统消息DTO
 */
@Data
@Schema(description = "更新系统消息请求")
public class SystemMessageUpdateDTO extends SystemMessageCreateDTO {

    /**
     * 消息ID
     */
    @NotNull(message = "消息ID不能为空")
    @Schema(description = "消息ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

} 