server:
  port: 8007
  tomcat:
    connection-timeout: 20000
spring:
  application:
    name: notify-service
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  data:
    redis:
      database: 0
      port: 6379
      host: localhost
      timeout: 10000
      client-type: lettuce
      lettuce:
        pool:
          # 连接池最大连接数（使用负值表示没有限制） 默认 8
          max-active: 20
          # 连接池中的最大空闲连接 默认 8
          max-idle: 10
          # 连接池中的最小空闲连接 默认 0
          min-idle: 3
          # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
          max-wait: 1s
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ***************************************************************
    username: postgres
    password: root
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848

mybatis-plus:
  ddl:
    application-runner-enabled: false
  mapper-locations: classpath*:/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  type-aliases-package: com.gw.notify.entity
  global-config:
    db-config:
      id-type: auto # 或者 assign_id, input, uuid, id_worker, snowflake 等其他策略
      # 逻辑删除配置
      logic-delete-field: deleted  # 全局逻辑删除字段名
      logic-delete-value: 1        # 逻辑已删除值
      logic-not-delete-value: 0    # 逻辑未删除值
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # 打印SQL日志
pagehelper:
  helper-dialect: postgresql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql
logging:
  config: classpath:log4j2.xml
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
    path: /api/v1/notify/swagger-ui.html
  paths-to-match:

feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 5000
  compression:
    request:
      enabled: true
    response:
      enabled: true
cache:
  prefix: lingxi
  configs:
    - name: agentTag
      expireAfterWrite: 720h
    - name: agentType
      expireAfterWrite: 720h
    - name: agent
      expireAfterWrite: 720h
    - name: coze
      expireAfterWrite: 720h

# 异步任务线程池配置
async:
  executor:
    thread:
      # 核心线程数
      core-pool-size: 4
      # 最大线程数
      max-pool-size: 8
      # 队列容量
      queue-capacity: 100
      # 线程池维护线程所允许的空闲时间，单位为秒
      keep-alive-seconds: 60
      # 线程名称前缀
      name-prefix: notify-async-
