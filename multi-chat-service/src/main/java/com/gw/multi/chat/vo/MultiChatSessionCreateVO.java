package com.gw.multi.chat.vo;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 多聊天会话创建结果VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "多聊天会话创建结果")
public class MultiChatSessionCreateVO {

    @Schema(description = "当前场景会话ID")
    private String sessionId;

    @Schema(description = "故事ID")
    private Long storyId;

    @Schema(description = "当前场景ID")
    private Long currentSceneId;

    public MultiChatSessionCreateVO(String sessionId) {
        this.sessionId = sessionId;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
} 