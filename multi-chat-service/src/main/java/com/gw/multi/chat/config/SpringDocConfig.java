package com.gw.multi.chat.config;

import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

import static org.springframework.web.reactive.function.server.RequestPredicates.GET;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

/**
 * SpringDoc配置
 * 用于配置Swagger文档
 */
@Configuration
public class SpringDocConfig {

    /**
     * 配置OpenAPI定义
     */
    @Bean
    public OpenAPI chatOpenAPI() {
        return new OpenAPI()
                .info(new Info().title("聊天故事服务API文档")
                        .description("提供聊天故事服务相关的接口文档")
                        .version("v1.0.0")
                        .contact(new Contact()
                                .name("开发团队")
                                .email("<EMAIL>"))
                        .license(new License().name("Apache 2.0").url("https://springdoc.org")))
                .externalDocs(new ExternalDocumentation()
                        .description("聊天故事服务文档")
                        .url("https://example.com/docs"));
    }

    /**
     * 配置API分组
     */
    @Bean
    public GroupedOpenApi chatApi() {
        return GroupedOpenApi.builder()
                .group("multi-chat-api")
                .pathsToMatch("/api/v1/multi/chat/**")
                .build();
    }

    /**
     * 添加Swagger UI重定向
     */
    @Bean
    public RouterFunction<ServerResponse> swaggerUiRedirectRoute() {
        return route(GET("/api/v1/multi/chat/swagger-ui.html"),
                req -> ServerResponse.temporaryRedirect(
                                req.exchange().getRequest().getURI()
                                        .resolve("/api/v1/multi/chat/swagger-ui/index.html"))
                        .build());
    }
}