package com.gw.multi.chat.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Speech configuration properties
 */
@Configuration
@ConfigurationProperties(prefix = "chat.speech")
@Data
public class SpeechConfig {

    /**
     * Temporary storage path for speech files
     */
    private String tempPath = "upload/file/chat/speech/tmp/";

    /**
     * Maximum directory size in bytes (default 5GB)
     */
    private long maxDirSize = 5368709120L; // 5GB in bytes

}
