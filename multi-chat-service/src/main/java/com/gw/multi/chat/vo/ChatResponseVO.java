package com.gw.multi.chat.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatResponseVO {
    private String chatId;
    private String agentName;
    private Long agentId;
    private LocalDateTime rspTime;
    private String content;
    private String remoteContextId;
    private String mentionTarget;
}
