package com.gw.multi.chat.dto;

import lombok.Data;

/**
 * 多智能体群聊消息DTO
 * <p>
 * 用于：
 * - WebSocket消息传输
 * - 客户端交互
 * - 服务间通信
 */
@Data
public class MultiChatMessageDTO {

    /**
     * 故事ID
     */
    private Long storyId;

    /**
     * 场景ID
     */
    private Long sceneId;
    private String sessionId;
    /**
     * 会话ID
     */
    private String storySessionId;

    private String sceneSessionId;
    /**
     * 用户名
     */
    private String username;

    /**
     * 智能体ID
     */
    private String agentId;

    /**
     * 智能体名称
     */
    private String agentName;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息状态
     */
    private String status;

} 