package com.gw.multi.chat.config;

import com.gw.common.agent.constant.AgentCommonCacheConstant;
import com.gw.common.agent.service.AgentProxyService;
import com.gw.common.agent.service.AgentStoryProxyService;
import com.gw.common.agent.vo.AgentStoryBaseVO;
import com.gw.common.dto.ResponseResult;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.RouterFunctions;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

import static org.springframework.web.reactive.function.server.RequestPredicates.GET;
import static org.springframework.web.reactive.function.server.RequestPredicates.POST;

/**
 * 路由配置类
 * 定义多智能体群聊服务的HTTP路由
 */
@Configuration
public class RouterConfig {

    private final AgentProxyService agentProxyService;
    private final AgentStoryProxyService agentStoryProxyService;
    private final CacheProperties cacheProperties;

    public RouterConfig(AgentProxyService agentProxyService,
                        AgentStoryProxyService agentStoryProxyService, CacheProperties cacheProperties) {
        this.agentProxyService = agentProxyService;
        this.agentStoryProxyService = agentStoryProxyService;
        this.cacheProperties = cacheProperties;
    }

    @Bean
    public RouterFunction<ServerResponse> multiChatRoutes() {
        return RouterFunctions
                .route(GET("/api/v1/multi-chat/story/{storyId}"), this::getStoryInfo)
                .andRoute(GET("/api/v1/multi-chat/scene/{sceneId}"), this::getSceneInfo)
                .andRoute(POST("/api/v1/multi-chat/session/create"), this::createSession)
                .andRoute(GET("/api/v1/multi-chat/websocket/info"), this::getWebSocketInfo)
                .andRoute(GET("/api/v1/multi-chat/health"), this::health);
    }

    /**
     * 获取故事信息
     */
    private Mono<ServerResponse> getStoryInfo(ServerRequest request) {
        Long storyId = Long.valueOf(request.pathVariable("storyId"));

        return Mono.fromCallable(() -> {
            try {
                String storyCacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_STORY_MAP_CACHE_KEY);
                AgentStoryBaseVO story = agentStoryProxyService.getStoryInfo(storyCacheKey, storyId);
                if (story != null) {
                    return ResponseResult.success(story);
                } else {
                    return ResponseResult.failure("故事不存在");
                }
            } catch (Exception e) {
                return ResponseResult.failure("获取故事信息失败: " + e.getMessage());
            }
        }).flatMap(result ->
                ServerResponse.ok()
                        .contentType(MediaType.APPLICATION_JSON)
                        .bodyValue(result)
        );
    }

    /**
     * 获取场景信息
     */
    private Mono<ServerResponse> getSceneInfo(ServerRequest request) {
        Long sceneId = Long.valueOf(request.pathVariable("sceneId"));

        return Mono.fromCallable(() -> {
            try {
                String sceneCacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_STORY_SCENE_KEY);

                Object scene = agentStoryProxyService.getStorySceneInfo(sceneId, sceneCacheKey);
                if (scene != null) {
                    return ResponseResult.success(scene);
                } else {
                    return ResponseResult.failure("场景不存在");
                }
            } catch (Exception e) {
                return ResponseResult.failure("获取场景信息失败: " + e.getMessage());
            }
        }).flatMap(result ->
                ServerResponse.ok()
                        .contentType(MediaType.APPLICATION_JSON)
                        .bodyValue(result)
        );
    }

    /**
     * 创建群聊会话
     */
    private Mono<ServerResponse> createSession(ServerRequest request) {
        String username = request.queryParam("username").orElse(null);
        String storyIdStr = request.queryParam("storyId").orElse(null);
        String sceneIdStr = request.queryParam("sceneId").orElse(null);

        if (username == null) {
            return ServerResponse.badRequest()
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(ResponseResult.failure("缺少username参数"));
        }

        if (storyIdStr == null) {
            return ServerResponse.badRequest()
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(ResponseResult.failure("缺少storyId参数"));
        }

        Long storyId = Long.valueOf(storyIdStr);
        Long sceneId = sceneIdStr != null ? Long.valueOf(sceneIdStr) : null;

        return Mono.fromCallable(() -> {
            try {
                // 获取故事信息
                String storyCacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_STORY_MAP_CACHE_KEY);

                AgentStoryBaseVO story = agentStoryProxyService.getStoryInfo(storyCacheKey, storyId);
                if (story == null) {
                    return ResponseResult.failure("故事不存在");
                }

                // 生成会话ID
                String sessionId = "session_" + username + "_" + storyId + "_" + System.currentTimeMillis();

                // 构建会话信息
                Map<String, Object> sessionInfo = new HashMap<>();
                sessionInfo.put("sessionId", sessionId);
                sessionInfo.put("username", username);
                sessionInfo.put("storyId", storyId);
                sessionInfo.put("sceneId", sceneId);
                sessionInfo.put("story", story);
                sessionInfo.put("createTime", System.currentTimeMillis());

                return ResponseResult.success(sessionInfo);
            } catch (Exception e) {
                return ResponseResult.failure("创建会话失败: " + e.getMessage());
            }
        }).flatMap(result ->
                ServerResponse.ok()
                        .contentType(MediaType.APPLICATION_JSON)
                        .bodyValue(result)
        );
    }

    /**
     * 获取WebSocket连接信息
     */
    private Mono<ServerResponse> getWebSocketInfo(ServerRequest request) {
        Map<String, String> info = new HashMap<>();
        info.put("endpoint", "/ws/multi/chat/message");
        info.put("protocol", "ws");
        info.put("description", "多智能体群聊WebSocket端点");

        ResponseResult<Map<String, String>> result = ResponseResult.success(info);

        return ServerResponse.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(result);
    }

    /**
     * 健康检查
     */
    private Mono<ServerResponse> health(ServerRequest request) {
        ResponseResult<String> result = ResponseResult.success("多智能体群聊服务运行正常");

        return ServerResponse.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(result);
    }
} 