package com.gw.multi.chat.vo;

import com.alibaba.fastjson2.JSONArray;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI响应VO
 */
@Data
public class AIResponseVO {

    /**
     * 远程上下文ID
     */
    private String remoteContextId;

    /**
     * 状态：completed, failed, running
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 完成时间
     */
    private LocalDateTime completedAt;

    /**
     * 失败时间
     */
    private LocalDateTime failedAt;

    /**
     * 响应上下文列表
     */
    private List<ChatContextVO> contexts;

    /**
     * 后续消息
     */
    private JSONArray followMsg;
    private Long agentId;
    private String agentName;
} 