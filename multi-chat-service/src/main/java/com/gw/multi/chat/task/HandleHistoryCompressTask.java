package com.gw.multi.chat.task;

import com.gw.common.agent.constant.AgentCommonCacheConstant;
import com.gw.common.agent.constant.StoryConstant;
import com.gw.common.agent.service.AgentStoryProxyService;
import com.gw.common.agent.vo.AgentStoryBaseVO;
import com.gw.common.agent.vo.AgentStorySceneBaseVO;
import com.gw.multi.chat.config.CacheProperties;
import com.gw.multi.chat.config.HistoryCompressConfig;
import com.gw.multi.chat.entity.MultiChatMsg;
import com.gw.multi.chat.entity.MultiChatMsgCompress;
import com.gw.multi.chat.entity.MultiChatSession;
import com.gw.multi.chat.exception.CompressTaskException;
import com.gw.multi.chat.exception.NonRetryableCompressException;
import com.gw.multi.chat.exception.RetryableCompressException;
import com.gw.multi.chat.service.MultiChatMsgCompressService;
import com.gw.multi.chat.service.MultiChatMsgService;
import com.gw.multi.chat.service.MultiChatSessionService;
import com.gw.multi.chat.service.VolcanoArkService;
import com.gw.multi.chat.util.ChatUtils;
import com.gw.multi.chat.vo.AIResponseVO;
import com.gw.multi.chat.vo.ChatContextVO;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.gw.multi.chat.constant.MultiChatConstant.CHAT_USER_ROLE;

/**
 * 历史记录压缩任务 - 优化版
 * <p>
 * 优化特性：
 * - 支持高并发处理
 * - 完全可配置
 * - 分布式锁防重复执行
 * - 监控指标收集
 * - 优雅错误处理
 * - 批量处理优化
 */
@Component
//@ConditionalOnProperty(prefix = "history.compress", name = "enabled", havingValue = "true", matchIfMissing = false)
@RequiredArgsConstructor
public class HandleHistoryCompressTask {

    private static final Logger log = LogManager.getLogger(HandleHistoryCompressTask.class);

    private final MultiChatSessionService chatSessionService;

    private final MultiChatMsgService chatMessageService;

    private final AgentStoryProxyService storyProxyService;

    private final CacheProperties cacheProperties;

    private final MultiChatMsgCompressService compressService;

    private final VolcanoArkService volcanoArkService;

    private final HistoryCompressConfig compressConfig;

    @Qualifier("historyCompressExecutor")
    private final Executor historyCompressExecutor;


    @Qualifier("aiRequestExecutor")
    private final Executor aiRequestExecutor;

    // 监控指标
    private final AtomicLong processedSessionsTotal = new AtomicLong(0);
    private final AtomicLong compressedScenesTotal = new AtomicLong(0);
    private final AtomicLong failedTasksTotal = new AtomicLong(0);
    private final AtomicInteger currentRunningTasks = new AtomicInteger(0);
    private final AtomicLong retryableFailuresTotal = new AtomicLong(0);
    private final AtomicLong nonRetryableFailuresTotal = new AtomicLong(0);
    private final AtomicLong batchedMessagesTotal = new AtomicLong(0);
    private final AtomicLong splitBatchesTotal = new AtomicLong(0);

    /**
     * 📊 历史记录切割配置
     * 单次压缩处理的最大消息数量，超过此数量将进行分批处理
     * 默认值：200条
     */
    @Value("${history.compress.max-messages-per-batch:200}")
    private int maxMessagesPerBatch;

    /**
     * 📊 历史记录切割重叠配置
     * 分批处理时的重叠消息数量，用于保持上下文连贯性
     * 默认值：20条
     */
    @Value("${history.compress.batch-overlap-size:20}")
    private int batchOverlapSize;
    /**
     * 压缩任务线程
     */
    private Thread compressionThread;
    /**
     * 线程运行标志
     */
    private volatile boolean running = false;

    /**
     * 初始化后验证配置
     */
    @PostConstruct
    private void validateConfiguration() {
        if (compressConfig.getAiRequestTimeout() <= 0) {
            log.warn("AI请求超时时间配置无效: {}ms，使用默认值30000ms",
                    compressConfig.getAiRequestTimeout());
        }

        if (compressConfig.getMaxSessionsPerBatch() <= 0) {
            log.warn("批处理会话数配置无效: {}，使用默认值10",
                    compressConfig.getMaxSessionsPerBatch());
        }

        if (maxMessagesPerBatch <= 0) {
            log.warn("单批最大消息数配置无效: {}，使用默认值200", maxMessagesPerBatch);
            maxMessagesPerBatch = 200;
        }

        if (batchOverlapSize < 0 || batchOverlapSize >= maxMessagesPerBatch) {
            log.warn("批次重叠大小配置无效: {}，使用默认值20", batchOverlapSize);
            batchOverlapSize = 20;
        }

        // 验证超时配置
        if (compressConfig.getPerTaskExtraTimeout() <= 0) {
            log.warn("每任务额外超时时间配置无效: {}ms，使用默认值10000ms",
                    compressConfig.getPerTaskExtraTimeout());
        }

        if (compressConfig.getMaxTotalTimeout() <= compressConfig.getAiRequestTimeout()) {
            log.warn("最大总超时时间配置过小: {}ms，应大于基础超时时间: {}ms",
                    compressConfig.getMaxTotalTimeout(), compressConfig.getAiRequestTimeout());
        }

        log.info("历史压缩任务配置验证完成 - 压缩阈值: {}, 批处理大小: {}, AI超时: {}ms, 单批最大消息数: {}, 重叠大小: {}, 每任务额外超时: {}ms, 最大总超时: {}ms",
                compressConfig.getCompressThreshold(),
                compressConfig.getMaxSessionsPerBatch(),
                compressConfig.getAiRequestTimeout(),
                maxMessagesPerBatch,
                batchOverlapSize,
                compressConfig.getPerTaskExtraTimeout(),
                compressConfig.getMaxTotalTimeout());
    }

    /**
     * 启动压缩任务线程
     */
    @PostConstruct
    public void startCompressionThread() {
        if (!compressConfig.isEnabled()) {
            log.info("历史记录压缩任务已禁用，不启动压缩线程");
            return;
        }

        running = true;
        compressionThread = new Thread(this::compressionWorker, "history-compress-worker");
        compressionThread.setDaemon(true); // 设置为守护线程
        compressionThread.start();

        log.info("历史记录压缩任务线程已启动，调度间隔: {}ms", compressConfig.getScheduleInterval());
    }

    /**
     * 停止压缩任务线程
     */
    @PreDestroy
    public void stopCompressionThread() {
        running = false;
        if (compressionThread != null && compressionThread.isAlive()) {
            compressionThread.interrupt();
            try {
                compressionThread.join(5000); // 等待最多5秒
                log.info("历史记录压缩任务线程已停止");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("等待压缩线程停止时被中断");
            }
        }
    }

    /**
     * 压缩任务工作线程
     */
    private void compressionWorker() {
        log.info("历史记录压缩工作线程开始运行");

        while (running && !Thread.currentThread().isInterrupted()) {
            try {
                // 执行压缩任务
                executeHistoryCompression();

                // 任务完成后休眠指定时间
                long sleepTime = compressConfig.getScheduleInterval();
                log.debug("压缩任务执行完成，休眠 {}ms", sleepTime);
                Thread.sleep(sleepTime);

            } catch (InterruptedException e) {
                log.info("压缩任务线程被中断，准备退出");
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("压缩任务执行异常，将在下次调度时重试", e);
                try {
                    // 发生异常时也要休眠，避免频繁重试
                    Thread.sleep(Math.min(compressConfig.getScheduleInterval(), 30000)); // 最多休眠30秒
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        log.info("历史记录压缩工作线程已退出");
    }

    /**
     * 手动触发压缩任务（用于测试和调试）
     */
    public void manualTriggerCompression() {
        if (!compressConfig.isEnabled()) {
            log.warn("历史记录压缩任务已禁用，无法手动触发");
            return;
        }

        log.info("手动触发历史记录压缩任务");
        try {
            executeHistoryCompression();
            log.info("手动触发的压缩任务执行完成");
        } catch (Exception e) {
            log.error("手动触发的压缩任务执行失败", e);
        }
    }

    /**
     * 执行历史记录压缩的核心逻辑
     */
    private void executeHistoryCompression() {
        long startTime = System.currentTimeMillis();
        log.info("开始执行历史记录压缩任务");

        try {

            // 获取需要压缩的会话列表
            List<MultiChatSession> needCompressSessions = chatSessionService.findNeedCompressSession();

            if (CollectionUtils.isEmpty(needCompressSessions)) {
                log.debug("没有需要压缩的会话");
                return;
            }

            log.info("发现 {} 个需要压缩的会话", needCompressSessions.size());

            // 分批处理会话
            List<List<MultiChatSession>> batches = partitionSessions(needCompressSessions);

            // 并发处理每个批次
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (int i = 0; i < batches.size(); i++) {
                final int batchIndex = i;
                final List<MultiChatSession> batch = batches.get(i);

                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    processBatch(batchIndex, batch);
                }, historyCompressExecutor);

                futures.add(future);
            }

            // 等待所有批次完成 - 优化超时控制策略
            try {
                // 计算合理的超时时间：基础超时 + 每个批次的额外时间，但设置上限
                long baseTimeout = compressConfig.getAiRequestTimeout();
                long perBatchTimeout = compressConfig.getPerTaskExtraTimeout();
                long calculatedTimeout = baseTimeout + (perBatchTimeout * futures.size());
                long totalTimeout = Math.min(calculatedTimeout, compressConfig.getMaxTotalTimeout());

                log.debug("批次处理超时配置 - 基础超时: {}ms, 每批次额外: {}ms, 计算超时: {}ms, 最终超时: {}ms, 批次数: {}",
                        baseTimeout, perBatchTimeout, calculatedTimeout, totalTimeout, futures.size());

                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                        .get(totalTimeout, TimeUnit.MILLISECONDS);

            } catch (TimeoutException e) {
                log.error("批次处理超时，已等待时间超过配置限制，取消所有未完成的任务。批次数: {}", futures.size());
                futures.forEach(future -> {
                    if (!future.isDone()) {
                        future.cancel(true);
                        log.debug("取消未完成的批次任务");
                    }
                });
                throw new RetryableCompressException("批次处理超时，可能是网络延迟或AI服务响应慢", e);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                futures.forEach(future -> future.cancel(true));
                throw new NonRetryableCompressException("批次处理被中断", e);
            } catch (ExecutionException e) {
                futures.forEach(future -> future.cancel(true));
                Throwable cause = e.getCause();
                if (cause instanceof CompressTaskException) {
                    throw (CompressTaskException) cause;
                }
                throw new NonRetryableCompressException("批次处理执行失败", cause);
            }


            long endTime = System.currentTimeMillis();
            log.info("历史记录压缩任务完成，耗时: {}ms", endTime - startTime);

        } catch (Exception e) {
            log.error("执行压缩任务失败", e);
            failedTasksTotal.incrementAndGet();
        }
    }

    /**
     * 分批处理会话
     */
    private List<List<MultiChatSession>> partitionSessions(List<MultiChatSession> sessions) {
        int batchSize = compressConfig.getMaxSessionsPerBatch();
        List<List<MultiChatSession>> batches = new ArrayList<>();

        for (int i = 0; i < sessions.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, sessions.size());
            batches.add(sessions.subList(i, endIndex));
        }

        return batches;
    }

    /**
     * 处理单个批次
     */
    private void processBatch(int batchIndex, List<MultiChatSession> batch) {
        currentRunningTasks.incrementAndGet();
        try {
            log.info("开始处理批次 {}, 包含 {} 个会话", batchIndex, batch.size());

            for (MultiChatSession session : batch) {
                try {
                    processSessionWithRetry(session);
                    processedSessionsTotal.incrementAndGet();
                } catch (CompressTaskException e) {
                    log.error("处理会话失败: {}", session.getSessionId(), e);
                    failedTasksTotal.incrementAndGet();
                    if (e.isRetryable()) {
                        retryableFailuresTotal.incrementAndGet();
                    } else {
                        nonRetryableFailuresTotal.incrementAndGet();
                    }
                } catch (Exception e) {
                    log.error("处理会话失败: {}", session.getSessionId(), e);
                    failedTasksTotal.incrementAndGet();
                    nonRetryableFailuresTotal.incrementAndGet();
                }
            }

            log.info("批次 {} 处理完成", batchIndex);

        } finally {
            currentRunningTasks.decrementAndGet();
        }
    }

    /**
     * 带重试的会话处理 - 改进重试逻辑，区分可重试和不可重试的异常
     */
    private void processSessionWithRetry(MultiChatSession session) {
        int retryCount = 0;
        Exception lastException = null;

        while (retryCount <= compressConfig.getMaxRetryCount()) {
            try {
                processSession(session);
                return; // 成功则直接返回
            } catch (CompressTaskException e) {
                lastException = e;

                // 如果是不可重试的异常，直接抛出
                if (!e.isRetryable()) {
                    log.error("处理会话遇到不可重试异常: {}", session.getSessionId(), e);
                    throw e;
                }

                retryCount++;
                if (retryCount <= compressConfig.getMaxRetryCount()) {
                    log.warn("处理会话失败，第{}次重试: {} - {}", retryCount, session.getSessionId(), e.getMessage());
                    try {
                        Thread.sleep(compressConfig.getRetryInterval());
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new NonRetryableCompressException("重试被中断", ie);
                    }
                } else {
                    log.error("处理会话重试耗尽: {}", session.getSessionId(), e);
                }
            } catch (Exception e) {
                lastException = e;
                retryCount++;

                if (retryCount <= compressConfig.getMaxRetryCount()) {
                    log.warn("处理会话失败，第{}次重试: {} - {}", retryCount, session.getSessionId(), e.getMessage());
                    try {
                        Thread.sleep(compressConfig.getRetryInterval());
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new NonRetryableCompressException("重试被中断", ie);
                    }
                } else {
                    log.error("处理会话重试耗尽: {}", session.getSessionId(), e);
                }
            }
        }

        // 重试耗尽，抛出最后一个异常
        throw new NonRetryableCompressException("处理会话重试耗尽: " + session.getSessionId(), lastException);
    }

    /**
     * 处理单个会话
     */
    private void processSession(MultiChatSession session) {
        try {
            String sessionId = session.getSessionId();
            if (compressConfig.isVerboseLogging()) {
                log.debug("开始处理会话: {}", sessionId);
            }

            String storyCacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_STORY_MAP_CACHE_KEY);
            AgentStoryBaseVO story = storyProxyService.getStoryInfo(storyCacheKey, session.getStoryId());

            if (!validateStory(story, sessionId)) {
                return;
            }

            // 处理会话的所有场景
            Map<Long, MultiChatSession.SceneSession> sceneMap = session.getSceneMap();
            if (sceneMap != null && !sceneMap.isEmpty()) {
                // 并发处理场景（如果场景数量较多）
                if (sceneMap.size() > 1) {
                    processScenesConcurrently(story, session, sceneMap);
                } else {
                    // 单个场景直接处理
                    for (Map.Entry<Long, MultiChatSession.SceneSession> entry : sceneMap.entrySet()) {
                        processSceneSession(story, session, entry.getKey(), entry.getValue());
                    }
                }
            } else {
                log.warn("会话 {} 没有场景数据", sessionId);
            }

        } catch (Exception e) {
            log.error("处理会话异常: {}", session.getSessionId(), e);
            throw e;
        }
    }

    /**
     * 并发处理多个场景
     */
    private void processScenesConcurrently(AgentStoryBaseVO story, MultiChatSession session,
                                           Map<Long, MultiChatSession.SceneSession> sceneMap) {
        List<CompletableFuture<Void>> sceneFutures = sceneMap.entrySet().stream()
                .map(entry -> CompletableFuture.runAsync(() -> {
                    try {
                        processSceneSession(story, session, entry.getKey(), entry.getValue());
                    } catch (Exception e) {
                        log.error("并发处理场景失败 - 场景ID: {}", entry.getKey(), e);
                        throw new RuntimeException(e);
                    }
                }, historyCompressExecutor))
                .collect(Collectors.toList());

        // 等待所有场景处理完成 - 优化超时控制策略
        try {
            // 为场景处理计算合理的超时时间
            long baseTimeout = compressConfig.getAiRequestTimeout();
            long perSceneTimeout = compressConfig.getPerTaskExtraTimeout();
            long calculatedTimeout = baseTimeout + (perSceneTimeout * sceneFutures.size());
            long totalTimeout = Math.min(calculatedTimeout, compressConfig.getMaxTotalTimeout());

            log.debug("场景并发处理超时配置 - 基础超时: {}ms, 每场景额外: {}ms, 计算超时: {}ms, 最终超时: {}ms, 场景数: {}",
                    baseTimeout, perSceneTimeout, calculatedTimeout, totalTimeout, sceneFutures.size());

            CompletableFuture.allOf(sceneFutures.toArray(new CompletableFuture[0]))
                    .get(totalTimeout, TimeUnit.MILLISECONDS);

        } catch (TimeoutException e) {
            log.error("场景并发处理超时，会话ID: {}, 场景数: {}, 取消所有未完成的场景任务",
                    session.getSessionId(), sceneFutures.size());
            sceneFutures.forEach(future -> {
                if (!future.isDone()) {
                    future.cancel(true);
                }
            });
            throw new RetryableCompressException("场景并发处理超时", e);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            sceneFutures.forEach(future -> future.cancel(true));
            throw new NonRetryableCompressException("场景并发处理被中断", e);
        } catch (ExecutionException e) {
            sceneFutures.forEach(future -> future.cancel(true));
            Throwable cause = e.getCause();
            if (cause instanceof CompressTaskException) {
                throw (CompressTaskException) cause;
            }
            throw new NonRetryableCompressException("场景并发处理执行失败", cause);
        }
    }

    /**
     * 处理单个场景会话
     */
    private void processSceneSession(AgentStoryBaseVO story, MultiChatSession session,
                                     Long sceneId, MultiChatSession.SceneSession sceneSession) {
        try {
            String sessionId = session.getSessionId();
            String sceneSessionId = sceneSession.getSceneSessionId();

            if (compressConfig.isVerboseLogging()) {
                log.debug("处理场景压缩 - 会话ID: {}, 场景ID: {}, 场景会话ID: {}",
                        sessionId, sceneId, sceneSessionId);
            }

            String sceneCacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_STORY_SCENE_KEY);
            AgentStorySceneBaseVO sceneVo = storyProxyService.getStorySceneInfo(sceneId, sceneCacheKey);

            if (sceneVo == null) {
                log.warn("场景 {} 的场景信息不存在或者已经删除", sceneSessionId);
                return;
            }

            // 执行场景历史记录压缩
            compressSceneHistory(story, session, sceneSession, sceneVo);
            compressedScenesTotal.incrementAndGet();

        } catch (Exception e) {
            log.error("处理场景会话异常 - 场景ID: {}, 场景会话ID: {}",
                    sceneId, sceneSession.getSceneSessionId(), e);
            throw e;
        }
    }

    /**
     * 验证故事状态
     */
    private boolean validateStory(AgentStoryBaseVO story, String sessionId) {
        if (story == null) {
            log.warn("故事信息不存在或者已经删除 - 会话ID: {}", sessionId);
            return false;
        }

        if (story.getShelfStatus() != StoryConstant.SHELF_ON_STATUS) {
            log.error("故事未上架 - 会话ID: {}, 故事名: {}, 上架状态: {}",
                    sessionId, story.getName(), story.getShelfStatus());
            return false;
        }

        if (story.getStatus() != StoryConstant.STORY_STATUS_PUBLISHED) {
            log.error("故事未发布 - 会话ID: {}, 故事名: {}, 发布状态: {}",
                    sessionId, story.getName(), story.getStatus());
            return false;
        }

        return true;
    }

    /**
     * 压缩指定场景的历史记录
     */
    private void compressSceneHistory(AgentStoryBaseVO story, MultiChatSession session,
                                      MultiChatSession.SceneSession sceneSession, AgentStorySceneBaseVO sceneVo) {
        try {
            String sceneSessionId = sceneSession.getSceneSessionId();
            Long sceneId = sceneSession.getSceneId();
            Long storyId = story.getId();

            // 获取或创建压缩记录
            MultiChatMsgCompress compressItem = compressService.findFirstMessage(storyId,
                    session.getSessionId(), sceneId, sceneSessionId);

            Long compressedSeqNum;
            if (compressItem == null) {
                compressedSeqNum = 0L;
                compressItem = createNewCompressItem(story, sceneId, session, sceneSession);
            } else {
                compressedSeqNum = compressItem.getCompressSeqNumber();
                // 修复数据一致性问题：确保使用正确的压缩序号
                if (compressedSeqNum == null) {
                    compressedSeqNum = 0L;
                }
            }

            // 检查是否需要压缩
            Long lstSeqNum = sceneSession.getLstSeqNum();
            if (lstSeqNum == null || lstSeqNum - compressedSeqNum <= compressConfig.getCompressThreshold()) {
                if (compressConfig.isVerboseLogging()) {
                    log.debug("场景 {} 的历史记录已经压缩过了，跳过。当前序号: {}, 已压缩序号: {}",
                            sceneVo.getSceneName(), lstSeqNum, compressedSeqNum);
                }
                return;
            }

            // 获取需要压缩的消息 - 修复数据一致性问题：使用正确的起始序号
            List<MultiChatMsg> messages = chatMessageService.findAllMessages(
                    session.getStoryId(), sceneId, compressedSeqNum, lstSeqNum);

            if (CollectionUtils.isEmpty(messages)) {
                if (compressConfig.isVerboseLogging()) {
                    log.debug("场景 {} 的历史记录为空，序号范围: {} - {}",
                            sceneVo.getSceneName(), compressedSeqNum, lstSeqNum);
                }
                return;
            }

            // 📊 检查是否需要分批处理
            if (messages.size() > maxMessagesPerBatch) {
                log.info("场景 {} 的消息数量 {} 超过单批限制 {}，将进行分批压缩",
                        sceneVo.getSceneName(), messages.size(), maxMessagesPerBatch);

                processMessagesBatch(messages, story, session, sceneSession, sceneVo, compressItem, lstSeqNum);
                return;
            }

            // 异步执行AI压缩 - 修复内存泄漏风险：添加超时控制
            MultiChatMsgCompress finalCompressItem = compressItem;
            CompletableFuture<String> compressionFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    String historyContent = buildHistoryContent(messages, story);
                    String existingSummary = finalCompressItem.getSummary();
                    if (existingSummary != null && !existingSummary.trim().isEmpty()) {
                        historyContent = existingSummary + "\n" + historyContent;
                    }
                    return performAICompression(story, sceneVo, historyContent);
                } catch (Exception e) {
                    log.error("AI压缩过程中发生异常", e);
                    throw new RuntimeException("AI压缩失败", e);
                }
            }, aiRequestExecutor);

            // 等待压缩结果（带动态超时） - 修复内存泄漏风险
            String compressedResult;
            try {
                // 根据历史内容长度动态计算超时时间
                String historyContent = buildHistoryContent(messages, story);
                long dynamicTimeout = calculateDynamicTimeout(
                        historyContent.length(),
                        compressConfig.getAiRequestTimeout()
                );

                compressedResult = compressionFuture.get(dynamicTimeout, TimeUnit.MILLISECONDS);

                log.debug("场景 {} AI压缩成功，内容长度: {}, 使用超时: {}ms",
                        sceneVo.getSceneName(), historyContent.length(), dynamicTimeout);

            } catch (TimeoutException e) {
                log.warn("场景 {} 的AI压缩请求超时，内容长度: {}, 超时时间: {}ms",
                        sceneVo.getSceneName(),
                        buildHistoryContent(messages, story).length(),
                        calculateDynamicTimeout(buildHistoryContent(messages, story).length(),
                                compressConfig.getAiRequestTimeout()));
                compressionFuture.cancel(true); // 取消任务以释放资源
                throw new RuntimeException("AI压缩超时", e);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                compressionFuture.cancel(true);
                throw new RuntimeException("AI压缩被中断", e);
            } catch (ExecutionException e) {
                compressionFuture.cancel(true);
                throw new RuntimeException("AI压缩执行失败", e.getCause());
            }

            if (compressedResult != null && !compressedResult.trim().isEmpty()) {
                // 保存压缩结果 - 确保数据一致性
                compressItem.setSummary(compressedResult);
                compressItem.setCompressSeqNumber(lstSeqNum);
                compressItem.setUpdateTime(LocalDateTime.now());
                compressService.saveSummary(compressItem);

                // 同步更新场景会话的压缩序号，确保数据一致性
                sceneSession.setCompressedSeqNum(lstSeqNum);
                chatSessionService.saveSession(session);

                log.info("场景 {} 的历史记录压缩完成，压缩序号: {}, 消息数量: {}",
                        sceneVo.getSceneName(), lstSeqNum, messages.size());
            } else {
                log.warn("场景 {} 的历史记录压缩失败，AI返回空结果", sceneVo.getSceneName());
            }

        } catch (Exception e) {
            log.error("压缩场景历史记录异常 - 场景: {}", sceneVo.getSceneName(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 创建新的压缩记录
     */
    private MultiChatMsgCompress createNewCompressItem(AgentStoryBaseVO story, Long sceneId, MultiChatSession session,
                                                       MultiChatSession.SceneSession sceneSession) {
        MultiChatMsgCompress compressItem = new MultiChatMsgCompress();
        compressItem.setId(UUID.randomUUID().toString());
        compressItem.setStoryId(story.getId());
        compressItem.setStorySessionId(session.getSessionId());
        compressItem.setSceneId(sceneId);
        compressItem.setSceneSessionId(sceneSession.getSceneSessionId());
        compressItem.setUsername(session.getUsername());
        compressItem.setSummary("");
        compressItem.setUpdateTime(LocalDateTime.now());
        return compressItem;
    }

    /**
     * 构建历史对话内容
     */
    private String buildHistoryContent(List<MultiChatMsg> messages, AgentStoryBaseVO story) {
        StringBuilder history = new StringBuilder();
        String myName = story.getMyName();

        for (MultiChatMsg message : messages) {
            String role = message.getRole();
            // 移除content中的心理内容（括号内容）
            String cleanContent = ChatUtils.removeBracketContent(message.getContent());

            if (CHAT_USER_ROLE.equals(role)) {
                history.append("【").append(myName).append("】：").append(cleanContent).append("\n");
            } else {
                String agentName = message.getAgentName();
                history.append("【").append(agentName).append("】：").append(cleanContent).append("\n");
            }
        }

        return history.toString();
    }

    /**
     * 📊 分批处理消息 - 处理超过限制的大量消息
     */
    private void processMessagesBatch(List<MultiChatMsg> allMessages, AgentStoryBaseVO story,
                                      MultiChatSession session, MultiChatSession.SceneSession sceneSession,
                                      AgentStorySceneBaseVO sceneVo, MultiChatMsgCompress compressItem,
                                      Long finalSeqNum) {

        String existingSummary = compressItem.getSummary();
        StringBuilder cumulativeSummary = new StringBuilder();

        if (existingSummary != null && !existingSummary.trim().isEmpty()) {
            cumulativeSummary.append(existingSummary).append("\n\n");
        }

        // 计算分批参数
        int totalMessages = allMessages.size();
        int batchCount = (int) Math.ceil((double) totalMessages / maxMessagesPerBatch);

        log.info("开始分批压缩场景 {} 的 {} 条消息，分为 {} 批，每批最多 {} 条，重叠 {} 条",
                sceneVo.getSceneName(), totalMessages, batchCount, maxMessagesPerBatch, batchOverlapSize);

        for (int batchIndex = 0; batchIndex < batchCount; batchIndex++) {
            try {
                // 计算当前批次的消息范围
                int startIndex = batchIndex * maxMessagesPerBatch;
                if (batchIndex > 0) {
                    // 从第二批开始，向前重叠一些消息以保持上下文
                    startIndex = Math.max(0, startIndex - batchOverlapSize);
                }

                int endIndex = Math.min(totalMessages, (batchIndex + 1) * maxMessagesPerBatch);
                List<MultiChatMsg> batchMessages = allMessages.subList(startIndex, endIndex);

                log.info("处理第 {}/{} 批消息，范围: {} - {}，消息数: {},前期摘要",
                        batchIndex + 1, batchCount, startIndex, endIndex - 1, batchMessages.size(), cumulativeSummary);

                // 构建当前批次的历史内容
                String batchHistoryContent = buildHistoryContent(batchMessages, story);

                // 如果有累积摘要，添加到当前批次内容前面
                if (cumulativeSummary.length() > 0) {
                    batchHistoryContent = "【前期摘要】\n" + cumulativeSummary.toString() +
                            "\n【当前对话】\n" + batchHistoryContent;
                }

                // 执行AI压缩 - 使用分批压缩的提示词
                boolean isFirstBatch = (batchIndex == 0);
                boolean hasPreviousSummary = (cumulativeSummary.length() > 0);
                String batchSummary = performAICompressionWithPrompt(story, sceneVo, batchHistoryContent,
                        isFirstBatch, hasPreviousSummary, batchIndex + 1, batchCount);

                if (batchSummary != null && !batchSummary.trim().isEmpty()) {
                    // 更新累积摘要
                    cumulativeSummary.setLength(0); // 清空之前的内容
                    cumulativeSummary.append(batchSummary);

                    log.info("第 {}/{} 批压缩完成，摘要长度: {} 字符",
                            batchIndex + 1, batchCount, batchSummary.length());
                } else {
                    log.warn("第 {}/{} 批压缩失败，AI返回空结果", batchIndex + 1, batchCount);
                    // 继续处理下一批，不中断整个流程
                }

                // 更新统计
                batchedMessagesTotal.addAndGet(batchMessages.size());
                splitBatchesTotal.incrementAndGet();

            } catch (Exception e) {
                log.error("处理第 {}/{} 批消息时发生异常", batchIndex + 1, batchCount, e);
                // 继续处理下一批，不中断整个流程
            }
        }

        // 保存最终的累积摘要
        if (cumulativeSummary.length() > 0) {
            compressItem.setSummary(cumulativeSummary.toString());
            compressItem.setCompressSeqNumber(finalSeqNum);
            compressItem.setUpdateTime(LocalDateTime.now());
            compressService.saveSummary(compressItem);

            // 同步更新场景会话的压缩序号
            sceneSession.setCompressedSeqNum(finalSeqNum);
            chatSessionService.saveSession(session);

            log.info("场景 {} 分批压缩完成，总消息数: {}，最终摘要长度: {} 字符",
                    sceneVo.getSceneName(), totalMessages, cumulativeSummary.length());
        } else {
            log.warn("场景 {} 分批压缩失败，未生成有效摘要", sceneVo.getSceneName());
        }
    }

    /**
     * 执行AI压缩 - 支持自定义提示词的分批压缩
     */
    private String performAICompressionWithPrompt(AgentStoryBaseVO story, AgentStorySceneBaseVO sceneVo, String historyContent, boolean isFirstBatch,
                                                  boolean hasPreviousSummary, int batchIndex, int totalBatches) {
        try {
            if (historyContent == null || historyContent.trim().isEmpty()) {
                log.warn("历史内容为空，跳过AI压缩");
                return null;
            }

            // 获取针对当前批次的优化提示词
            String compressionPrompt = getCompressionPrompt(story, sceneVo, isFirstBatch, story.getMyName(), hasPreviousSummary, batchIndex, totalBatches);


            List<ChatContextVO> contexts = buildCompressContextsWithPrompt(historyContent, compressionPrompt);

            log.debug("执行第 {}/{} 批AI压缩，提示词长度: {} 字符，内容长度: {} 字符",
                    batchIndex, totalBatches, compressionPrompt.length(), historyContent.length());

            AIResponseVO response = volcanoArkService.sendChatMessageTraditional(contexts);

            if (response != null && response.getContexts() != null && !response.getContexts().isEmpty()) {
                String result = response.getContexts().get(0).getContent();
                if (result != null && !result.trim().isEmpty()) {
                    log.debug("第 {}/{} 批AI压缩成功，输出长度: {} 字符", batchIndex, totalBatches, result.length());


                    return result;
                } else {
                    log.warn("第 {}/{} 批AI返回了空的压缩结果", batchIndex, totalBatches);
                    return null;
                }
            }

            log.warn("第 {}/{} 批AI压缩响应为空或格式不正确", batchIndex, totalBatches);
            return null;
        } catch (Exception e) {
            // 异常处理保持不变
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
                log.error("第 {}/{} 批AI压缩被中断", batchIndex, totalBatches, e);
                throw new NonRetryableCompressException("AI压缩被中断", e);
            } else if (e instanceof TimeoutException) {
                log.warn("第 {}/{} 批AI压缩请求超时", batchIndex, totalBatches, e);
                throw new RetryableCompressException("AI压缩超时", e);
            } else if (e.getMessage() != null &&
                    (e.getMessage().contains("网络") || e.getMessage().contains("连接") ||
                            e.getMessage().contains("服务不可用"))) {
                log.warn("第 {}/{} 批AI压缩网络相关错误", batchIndex, totalBatches, e);
                throw new RetryableCompressException("AI压缩网络错误", e);
            } else {
                log.error("第 {}/{} 批AI压缩失败", batchIndex, totalBatches, e);
                throw new NonRetryableCompressException("AI压缩失败", e);
            }
        }
    }

    /**
     * 执行AI压缩 - 改进异常处理，区分可重试和不可重试的异常（兼容原有调用）
     */
    private String performAICompression(AgentStoryBaseVO story, AgentStorySceneBaseVO sceneVo, String historyContent) {
        try {
            if (historyContent == null || historyContent.trim().isEmpty()) {
                log.warn("历史内容为空，跳过AI压缩");
                return null;
            }

            List<ChatContextVO> contexts = buildCompressContexts(story, sceneVo, historyContent);
            AIResponseVO response = volcanoArkService.sendChatMessageTraditional(contexts);

            if (response != null && response.getContexts() != null && !response.getContexts().isEmpty()) {
                String result = response.getContexts().get(0).getContent();
                if (result != null && !result.trim().isEmpty()) {
                    return result;
                } else {
                    log.warn("AI返回了空的压缩结果");
                    return null;
                }
            }

            log.warn("AI压缩响应为空或格式不正确");
            return null;
        } catch (Exception e) {
            // 区分不同类型的异常，使用自定义异常类
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
                log.error("AI压缩被中断", e);
                throw new NonRetryableCompressException("AI压缩被中断", e);
            } else if (e instanceof TimeoutException) {
                log.warn("AI压缩请求超时", e);
                throw new RetryableCompressException("AI压缩超时", e);
            } else if (e.getMessage() != null &&
                    (e.getMessage().contains("网络") || e.getMessage().contains("连接") ||
                            e.getMessage().contains("服务不可用"))) {
                log.warn("AI压缩网络相关错误", e);
                throw new RetryableCompressException("AI压缩网络错误", e);
            } else {
                log.error("AI压缩失败", e);
                throw new NonRetryableCompressException("AI压缩失败", e);
            }
        }
    }

    /**
     * 构建压缩上下文
     */
    private List<ChatContextVO> buildCompressContexts(AgentStoryBaseVO story, AgentStorySceneBaseVO sceneVo, String historyContent) {
        List<ChatContextVO> contexts = new ArrayList<>();

        // 系统提示词
        contexts.add(createChatContextVO("system", "text", getCompressionPrompt(story, sceneVo), "text"));

        // 用户对话内容
        contexts.add(createChatContextVO("user", "text", historyContent, "text"));

        return contexts;
    }

    /**
     * 创建ChatContextVO对象
     */
    private ChatContextVO createChatContextVO(String role, String type, String content, String contentType) {
        ChatContextVO context = new ChatContextVO();
        context.setRole(role);
        context.setType(type);
        context.setContent(content);
        context.setContentType(contentType);
        return context;
    }

    /**
     * 获取压缩提示词
     */
    /**
     * 获取压缩提示词 - 支持不同压缩模式
     *
     * @param isFirstBatch       是否为第一批（完整压缩）
     * @param hasPreviousSummary 是否有前期摘要
     * @param batchIndex         当前批次索引（从1开始）
     * @param totalBatches       总批次数
     * @return 优化的压缩提示词
     */
    private String getCompressionPrompt(AgentStoryBaseVO story, AgentStorySceneBaseVO sceneVo,
                                        boolean isFirstBatch, String mainRole, boolean hasPreviousSummary,
                                        int batchIndex, int totalBatches) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("# 故事情节压缩指南\n\n");

        // 添加故事背景信息
        prompt.append("## 故事背景\n");
        prompt.append("- **故事名称**：").append(story.getName()).append("\n");
        prompt.append("- **主角设定**：").append(story.getMyName()).append("（").append(mainRole).append("）\n");

        prompt.append("\n");

        prompt.append("## 核心原则\n");
        prompt.append("- 保留所有重要情节细节和角色互动\n");
        prompt.append("- 按时间顺序详细记录事件发展\n");
        prompt.append("- 明确记录关系变化的具体表现\n");
        prompt.append("- 保留关键对话和情感表达\n");
        prompt.append("- 记录所有约定、承诺和未来计划\n");
        prompt.append("- 特别关注与主角【").append(mainRole).append("】的互动发展\n\n");

        if (totalBatches > 1) {
            prompt.append("## 分批处理说明\n");
            prompt.append("当前处理：第").append(batchIndex).append("/").append(totalBatches).append("批\n");
            if (hasPreviousSummary) {
                prompt.append("- 已有前期摘要，请在此基础上继续补充\n");
            }
            if (batchIndex < totalBatches) {
                prompt.append("- 这不是最后一批，重点记录当前时间段的详细事件\n");
            }
            prompt.append("\n");
        }

        prompt.append("## 输出格式要求\n\n");

        prompt.append("### 【人物档案】\n");
        prompt.append("#### 主要角色信息\n");

        // 主角信息模板
        prompt.append("**").append(mainRole).append("**（主角）\n");
        prompt.append("- **身份职业**：[具体职业/身份]\n");
        prompt.append("- **年龄外貌**：[年龄段及外貌特征]\n");
        prompt.append("- **性格特征**：[主要性格特点，用形容词描述]\n");
        prompt.append("- **行为特点**：[典型行为模式或口头禅]\n");
        prompt.append("- **背景信息**：[重要背景，如家庭、经历等]\n");
        prompt.append("- **当前状态**：[在故事中的当前状况]\n\n");

        // 其他角色信息模板
        prompt.append("**[其他角色姓名]**\n");
        prompt.append("- **身份职业**：[具体职业/身份]\n");
        prompt.append("- **年龄外貌**：[年龄段及外貌特征]\n");
        prompt.append("- **性格特征**：[主要性格特点，用形容词描述]\n");
        prompt.append("- **行为特点**：[典型行为模式或口头禅]\n");
        prompt.append("- **背景信息**：[重要背景，如家庭、经历等]\n");
        prompt.append("- **当前状态**：[在故事中的当前状况]\n");
        prompt.append("- **与").append(mainRole).append("的关系**：[具体关系描述]\n\n");

        prompt.append("#### 人物关系图谱\n");
        prompt.append("```\n");
        prompt.append("[").append(mainRole).append("] ←→ [角色A]：[关系类型] - [亲密度] - [关系状态]\n");
        prompt.append("[").append(mainRole).append("] ←→ [角色B]：[关系类型] - [亲密度] - [关系状态]\n");
        prompt.append("[角色A] ←→ [角色B]：[彼此关系] - [互动情况]\n");
        prompt.append("```\n\n");

        prompt.append("#### 关系发展轨迹\n");
        prompt.append("- **[角色A]与").append(mainRole).append("**：[初识] → [发展过程] → [当前状态] → [未来趋势]\n");
        prompt.append("- **[角色B]与").append(mainRole).append("**：[初识] → [发展过程] → [当前状态] → [未来趋势]\n\n");

        prompt.append("### 【时间线发展】\n");
        prompt.append("按时间顺序详细记录：\n\n");

        prompt.append("#### 早期事件（如有）\n");
        prompt.append("- **时间**：[具体时间]\n");
        prompt.append("- **地点**：[具体地点]\n");
        prompt.append("- **参与者**：[人物名单]\n");
        prompt.append("- **事件**：[详细事件描述]\n");
        prompt.append("- **关键对话**：[重要对话内容]\n");
        prompt.append("- **情感变化**：[具体情感或关系变化]\n");
        prompt.append("- **").append(mainRole).append("的反应**：[主角的具体反应和想法]\n");
        prompt.append("- **结果/影响**：[对后续情节的影响]\n\n");

        prompt.append("#### 中期发展\n");
        prompt.append("[使用相同格式记录]\n\n");

        prompt.append("#### 近期核心事件（重点详述）\n");
        prompt.append("[使用相同格式，但更加详细]\n\n");

        prompt.append("### 【性格分析与行为模式】\n");
        prompt.append("#### 性格特征详述\n");

        // 主角性格分析
        prompt.append("**").append(mainRole).append("**：\n");
        prompt.append("- **核心性格**：[3-5个关键词描述主要性格]\n");
        prompt.append("- **情感表达**：[如何表达情感，内向/外向等]\n");
        prompt.append("- **处事风格**：[面对问题的典型反应]\n");
        prompt.append("- **人际倾向**：[与他人交往的特点]\n");
        prompt.append("- **成长变化**：[在故事中性格的发展变化]\n");
        prompt.append("- **魅力特质**：[吸引其他角色的特点]\n\n");

        // 其他角色性格分析
        prompt.append("**[其他角色姓名]**：\n");
        prompt.append("- **核心性格**：[3-5个关键词描述主要性格]\n");
        prompt.append("- **情感表达**：[如何表达情感，内向/外向等]\n");
        prompt.append("- **处事风格**：[面对问题的典型反应]\n");
        prompt.append("- **人际倾向**：[与他人交往的特点]\n");
        prompt.append("- **成长变化**：[在故事中性格的发展变化]\n");
        prompt.append("- **对").append(mainRole).append("的态度**：[具体态度和情感倾向]\n\n");

        prompt.append("#### 典型对话风格\n");
        prompt.append("- **").append(mainRole).append("**：[说话特点、口头禅、语言风格]\n");
        prompt.append("- **[角色A]**：[说话特点、口头禅、语言风格]\n");
        prompt.append("- **[角色B]**：[说话特点、口头禅、语言风格]\n\n");

        prompt.append("### 【关系动态分析】\n");
        prompt.append("#### 关系亲密度等级\n");
        prompt.append("- **陌生人**：初次见面，无深入了解\n");
        prompt.append("- **朋友**：有一定了解，偶尔互动\n");
        prompt.append("- **好友**：深入了解，经常互动，互相关心\n");
        prompt.append("- **亲密**：深度情感连接，频繁亲密互动\n");
        prompt.append("- **恋人**：确立恋爱关系，有排他性\n\n");

        prompt.append("#### 当前关系状态矩阵\n");
        prompt.append("```\n");
        prompt.append("        │ [角色A] │ [角色B] │ [角色C] │\n");
        prompt.append("────────┼─────────┼─────────┼─────────┤\n");
        prompt.append("[").append(mainRole).append("]  │ [等级]  │ [等级]  │ [等级]  │\n");
        prompt.append("[角色A] │   ---   │ [关系]  │ [关系]  │\n");
        prompt.append("[角色B] │   ---   │   ---   │ [关系]  │\n");
        prompt.append("```\n\n");

        prompt.append("### 【重要承诺与约定】\n");
        prompt.append("- **约定内容**：[具体内容]\n");
        prompt.append("- **参与人**：[相关人员]\n");
        prompt.append("- **时间地点**：[具体安排]\n");
        prompt.append("- **").append(mainRole).append("的承诺**：[主角的具体承诺]\n");
        prompt.append("- **当前状态**：[是否完成/进行中/冲突]\n\n");

        prompt.append("### 【关系发展状态】\n");
        prompt.append("详细记录每对关系的当前状态：\n");
        prompt.append("- **").append(mainRole).append(" ↔ [角色A]**：[关系类型] - [最新发展] - [亲密程度] - [未解决问题]\n");
        prompt.append("- **").append(mainRole).append(" ↔ [角色B]**：[关系类型] - [最新发展] - [亲密程度] - [未解决问题]\n");
        prompt.append("- **[角色A] ↔ [角色B]**：[关系类型] - [最新发展] - [亲密程度] - [未解决问题]\n\n");

        prompt.append("### 【情节冲突与张力】\n");
        prompt.append("- **当前冲突**：[具体冲突描述]\n");
        prompt.append("- **").append(mainRole).append("的困境**：[主角面临的主要困难]\n");
        prompt.append("- **潜在问题**：[可能的未来冲突]\n");
        prompt.append("- **情感张力**：[角色间的情感紧张点]\n");
        prompt.append("- **选择压力**：[").append(mainRole).append("需要做出的重要选择]\n\n");

        prompt.append("### 【未完成线索】\n");
        prompt.append("- **悬而未决的事情**：[具体描述]\n");
        prompt.append("- **等待").append(mainRole).append("回应的行动**：[其他角色在等待什么]\n");
        prompt.append("- **").append(mainRole).append("的待办事项**：[主角需要处理的事情]\n");
        prompt.append("- **后续发展预期**：[可能的走向]\n\n");

        // 根据场景特点添加特殊指引
        if (sceneVo != null) {
            prompt.append("### 【场景特定分析】\n");
            prompt.append("- **场景氛围**：[").append(sceneVo.getSceneName()).append("的整体氛围]\n");
            prompt.append("- **场景意义**：[这个场景在故事中的重要性]\n");
            prompt.append("- **环境影响**：[场景环境对角色行为的影响]\n");
            prompt.append("- **场景推进**：[场景如何推动剧情发展]\n\n");
        }

        prompt.append("## 特别注意\n");
        prompt.append("1. 所有具体的地点、时间、人名都要准确记录\n");
        prompt.append("2. 情感表达和亲密行为要具体描述程度\n");
        prompt.append("3. 对话中的承诺、约定要单独记录\n");
        prompt.append("4. 多人场景中每个人的反应都要记录\n");
        prompt.append("5. 保留所有可能影响后续剧情的细节\n");
        prompt.append("6. 重点关注").append(mainRole).append("的心理变化和决策过程\n");
        prompt.append("7. 记录其他角色对").append(mainRole).append("的不同态度和期待\n\n");

        prompt.append("请开始分析压缩：\n");

        return prompt.toString();
    }


    /**
     * 获取压缩提示词 - 兼容原有调用（单批压缩）
     */
    private String getCompressionPrompt(AgentStoryBaseVO story, AgentStorySceneBaseVO sceneVo) {
        return getCompressionPrompt(story, sceneVo, true, story.getMyName(), false, 1, 1);
    }

    /**
     * 构建压缩上下文 - 支持自定义提示词
     */
    private List<ChatContextVO> buildCompressContextsWithPrompt(String historyContent, String customPrompt) {
        List<ChatContextVO> contexts = new ArrayList<>();

        // 添加自定义系统提示
        contexts.add(new ChatContextVO("system", customPrompt));

        // 构建用户消息，明确这是需要压缩的历史记录
        String userMessage = "以下是需要压缩的历史对话记录：\n\n" + historyContent + "\n\n请按照系统要求进行压缩分析。";
        contexts.add(new ChatContextVO("user", userMessage));

        return contexts;
    }


    /**
     * 根据内容复杂度动态计算超时时间
     *
     * @param contentLength 内容长度
     * @param baseTimeout   基础超时时间
     * @return 调整后的超时时间
     */
    private long calculateDynamicTimeout(int contentLength, long baseTimeout) {
        // 根据内容长度动态调整超时时间
        // 每1000个字符增加10秒超时时间
        long extraTimeout = (contentLength / 1000) * 10000;

        // 最大不超过基础超时时间的3倍
        long maxTimeout = baseTimeout * 3;
        long adjustedTimeout = baseTimeout + extraTimeout;

        long finalTimeout = Math.min(adjustedTimeout, maxTimeout);

        if (finalTimeout > baseTimeout) {
            log.debug("根据内容长度 {} 调整超时时间：{} -> {} ms",
                    contentLength, baseTimeout, finalTimeout);
        }

        return finalTimeout;
    }

    /**
     * 获取任务监控指标
     */
    public String getMetrics() {
        if (!compressConfig.isMetricsEnabled()) {
            return "监控指标已禁用";
        }

        return String.format(
                "压缩任务监控指标 - 处理会话总数: %d, 压缩场景总数: %d, 失败任务总数: %d, 当前运行任务数: %d, 可重试失败: %d, 不可重试失败: %d, 分批处理消息总数: %d, 分割批次总数: %d",
                processedSessionsTotal.get(),
                compressedScenesTotal.get(),
                failedTasksTotal.get(),
                currentRunningTasks.get(),
                retryableFailuresTotal.get(),
                nonRetryableFailuresTotal.get(),
                batchedMessagesTotal.get(),
                splitBatchesTotal.get()
        );
    }
}
