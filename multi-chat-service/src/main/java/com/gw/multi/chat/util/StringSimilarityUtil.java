package com.gw.multi.chat.util;

import lombok.extern.log4j.Log4j2;

/**
 * 字符串相似度计算工具类
 * 提供多种字符串相似度计算算法
 */
@Log4j2
public class StringSimilarityUtil {

    /**
     * 计算两个字符串的相似度（基于编辑距离）
     * 
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 相似度值，范围0.0-1.0，1.0表示完全相同
     */
    public static double calculateSimilarity(String str1, String str2) {
        if (str1 == null || str2 == null) {
            return 0.0;
        }
        
        // 去除空格并转换为小写进行比较
        String s1 = str1.trim().toLowerCase();
        String s2 = str2.trim().toLowerCase();
        
        if (s1.equals(s2)) {
            return 1.0;
        }
        
        if (s1.isEmpty() || s2.isEmpty()) {
            return 0.0;
        }
        
        // 使用编辑距离算法计算相似度
        int editDistance = calculateEditDistance(s1, s2);
        int maxLength = Math.max(s1.length(), s2.length());
        
        // 相似度 = 1 - (编辑距离 / 最大长度)
        double similarity = 1.0 - (double) editDistance / maxLength;
        
        log.debug("字符串相似度计算: [{}] vs [{}] = {}", str1, str2, similarity);
        
        return Math.max(0.0, similarity);
    }

    /**
     * 计算编辑距离（Levenshtein Distance）
     * 
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 编辑距离
     */
    private static int calculateEditDistance(String str1, String str2) {
        int len1 = str1.length();
        int len2 = str2.length();
        
        // 创建动态规划表
        int[][] dp = new int[len1 + 1][len2 + 1];
        
        // 初始化边界条件
        for (int i = 0; i <= len1; i++) {
            dp[i][0] = i;
        }
        for (int j = 0; j <= len2; j++) {
            dp[0][j] = j;
        }
        
        // 填充动态规划表
        for (int i = 1; i <= len1; i++) {
            for (int j = 1; j <= len2; j++) {
                if (str1.charAt(i - 1) == str2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = Math.min(
                        Math.min(dp[i - 1][j] + 1, dp[i][j - 1] + 1),
                        dp[i - 1][j - 1] + 1
                    );
                }
            }
        }
        
        return dp[len1][len2];
    }

    /**
     * 计算两个字符串的最长公共子序列相似度
     * 
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 相似度值，范围0.0-1.0
     */
    public static double calculateLCSSimilarity(String str1, String str2) {
        if (str1 == null || str2 == null) {
            return 0.0;
        }
        
        String s1 = str1.trim().toLowerCase();
        String s2 = str2.trim().toLowerCase();
        
        if (s1.equals(s2)) {
            return 1.0;
        }
        
        if (s1.isEmpty() || s2.isEmpty()) {
            return 0.0;
        }
        
        int lcsLength = calculateLCS(s1, s2);
        int maxLength = Math.max(s1.length(), s2.length());
        
        return (double) lcsLength / maxLength;
    }

    /**
     * 计算最长公共子序列长度
     * 
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 最长公共子序列长度
     */
    private static int calculateLCS(String str1, String str2) {
        int len1 = str1.length();
        int len2 = str2.length();
        
        int[][] dp = new int[len1 + 1][len2 + 1];
        
        for (int i = 1; i <= len1; i++) {
            for (int j = 1; j <= len2; j++) {
                if (str1.charAt(i - 1) == str2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1] + 1;
                } else {
                    dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
                }
            }
        }
        
        return dp[len1][len2];
    }

    /**
     * 计算字符串包含相似度
     * 检查一个字符串是否包含另一个字符串的大部分字符
     * 
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 相似度值，范围0.0-1.0
     */
    public static double calculateContainsSimilarity(String str1, String str2) {
        if (str1 == null || str2 == null) {
            return 0.0;
        }
        
        String s1 = str1.trim().toLowerCase();
        String s2 = str2.trim().toLowerCase();
        
        if (s1.equals(s2)) {
            return 1.0;
        }
        
        if (s1.isEmpty() || s2.isEmpty()) {
            return 0.0;
        }
        
        // 检查较短字符串是否包含在较长字符串中
        String shorter = s1.length() <= s2.length() ? s1 : s2;
        String longer = s1.length() > s2.length() ? s1 : s2;
        
        if (longer.contains(shorter)) {
            return (double) shorter.length() / longer.length();
        }
        
        // 计算字符重叠度
        int commonChars = 0;
        boolean[] used = new boolean[longer.length()];
        
        for (char c : shorter.toCharArray()) {
            for (int i = 0; i < longer.length(); i++) {
                if (!used[i] && longer.charAt(i) == c) {
                    used[i] = true;
                    commonChars++;
                    break;
                }
            }
        }
        
        return (double) commonChars / Math.max(s1.length(), s2.length());
    }

    /**
     * 综合相似度计算
     * 结合编辑距离和最长公共子序列的综合相似度
     * 
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 综合相似度值，范围0.0-1.0
     */
    public static double calculateComprehensiveSimilarity(String str1, String str2) {
        double editSimilarity = calculateSimilarity(str1, str2);
        double lcsSimilarity = calculateLCSSimilarity(str1, str2);
        double containsSimilarity = calculateContainsSimilarity(str1, str2);
        
        // 加权平均：编辑距离40%，LCS 30%，包含相似度30%
        return editSimilarity * 0.4 + lcsSimilarity * 0.3 + containsSimilarity * 0.3;
    }
}
