package com.gw.multi.chat.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "会话内容")
public class ChatHistoryContentVO {
    private String chatId;
    private String content;
    private String role;
    private Long agentId;
    private LocalDateTime updateTime;
}
