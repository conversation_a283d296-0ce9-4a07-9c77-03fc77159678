package com.gw.gateway.service.impl;

import com.gw.common.exception.BusinessExceptionCode;
import com.gw.gateway.TokenRsp;
import com.gw.gateway.service.ReactiveUserAuthService;
import com.gw.gateway.vo.TokenInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
@Log4j2
public class ReactiveUserAuthServiceImpl implements ReactiveUserAuthService {
    private final WebClient.Builder clientBuilder;


    @Override
    public Mono<TokenRsp> validateToken(String token) {

        return clientBuilder
                .build()
                .post()
                .uri(uriBuilder -> uriBuilder
                        .scheme("http")
                        .host("user-service")
                        .path("/api/v1/auth/validate")
                        .build())
                .header("Authorization", "Bearer " + token)
                .retrieve()
                .bodyToMono(TokenRsp.class)
                .onErrorResume(throwable -> {
                    log.error("Token validation failed", throwable);
                    TokenRsp rsp = new TokenRsp();
                    rsp.setCode(BusinessExceptionCode.FAIL_CODE.getCode());
                    rsp.setMsg(BusinessExceptionCode.FAIL_CODE.getMsg());
                    rsp.setData(TokenInfo.invalid());
                    return Mono.just(rsp);
                });
    }

    @Override
    public Mono<Boolean> checkPermission(String username, String path) {
        return clientBuilder.build()
                .get()
                .uri(uriBuilder -> uriBuilder
                        .scheme("http")
                        .host("user-service")
                        .path("/auth/check-permission")
                        .queryParam("username", username)
                        .queryParam("path", path)
                        .build())
                .retrieve()
                .bodyToMono(Boolean.class)
                .onErrorReturn(false);
    }
}
