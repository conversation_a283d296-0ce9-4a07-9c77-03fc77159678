package com.gw.gateway.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.reactive.socket.client.ReactorNettyWebSocketClient;
import org.springframework.web.reactive.socket.client.WebSocketClient;
import org.springframework.web.reactive.socket.server.WebSocketService;
import org.springframework.web.reactive.socket.server.support.HandshakeWebSocketService;
import org.springframework.web.reactive.socket.server.support.WebSocketHandlerAdapter;
import org.springframework.web.reactive.socket.server.upgrade.ReactorNettyRequestUpgradeStrategy;

/**
 * WebSocket客户端配置类
 * 独立提供WebSocketClient和相关组件，避免循环依赖
 */
@Configuration
public class WebSocketClientConfig {

    /**
     * 提供Reactor Netty WebSocket客户端
     */
    @Bean
    @Primary
    public WebSocketClient webSocketClient() {
        return new ReactorNettyWebSocketClient();
    }

    /**
     * 提供基于Reactor Netty的WebSocket升级策略
     */
    @Bean
    @Primary
    public WebSocketService webSocketService() {
        return new HandshakeWebSocketService(new ReactorNettyRequestUpgradeStrategy());
    }

    /**
     * 配置WebSocket处理适配器
     */
    @Bean
    public WebSocketHandlerAdapter handlerAdapter(WebSocketService webSocketService) {
        return new WebSocketHandlerAdapter(webSocketService);
    }
}