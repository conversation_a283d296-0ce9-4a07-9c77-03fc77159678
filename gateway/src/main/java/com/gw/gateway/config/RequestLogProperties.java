package com.gw.gateway.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "logging.filter.request")
public class RequestLogProperties {
    private boolean enabled = true;
    private boolean includePayload = false;
    private boolean includeHeaders = false;
}