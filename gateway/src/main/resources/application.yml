spring:
  application:
    name: api-gateway
  main:
    web-application-type: reactive
    allow-circular-references: true
    allow-bean-definition-overriding: true
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ******************************************
    username: postgres
    password: root
  cloud:
    gateway:
      globalcors:
        corsConfigurations:
          "[/**]":
            allowedOrigins: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
      discovery:
        locator:
          enabled: true
      routes:
        - id: user-service
          uri: lb://user-service
          predicates:
            - Path=/api/v1/auth/**, /auth/**
          filters:
            # 使用自定义过滤器（需要配置对应的过滤器工厂）
            - name: CustomHeader
              args:
                enabled: true
        - id: agent-service
          uri: lb://agent-service
          predicates:
            - Path=/api/v1/agent/**
        - id: chat-service
          uri: lb://chat-service
          predicates:
            - Path=/api/v1/chat/**
        - id: notify-service
          uri: lb://notify-service
          predicates:
            - Path=/api/v1/notify/**
        - id: membership-service
          uri: lb://membership-service
          predicates:
            - Path=/api/v1/membership/**
        - id: multi-chat-service
          uri: lb://multi-chat-service
          predicates:
            - Path=/api/v1/multi/chat/**
    loadbalancer:
      enabled: true

    nacos:
      discovery:
        server-addr: localhost:8848
      config:
        server-addr: localhost:8848
  data:
    redis:
      timeout: 10000
      host: localhost
      port: 6379
      database: 0
server:
  port: 8000
  tomcat:
    connection-timeout: 20000

auth:
  skip:
    urls:
      - /api/v1/auth/login/**
      - /api/v1/auth/organize/tree/username/**
      - /api/v1/agent/public/page
      - /api/v1/notify/system_message/public/get
      - /api/v1/agent/recommend/system/page
      - /api/v1/membership/orders/wxpay/notify
      - /api/v1/membership/orders/query/iot_pay_enable
logging:
  config: classpath:log4j2-spring.xml
  level:
    root: INFO
    com.ca.gateway: INFO
    org.springframework.cloud.gateway: DEBUG
    org.springframework.web.server: INFO
    org.springframework.cloud.loadbalancer: DEBUG
    org.springframework.web.socket: DEBUG
    com.gw.gateway.websocket: DEBUG
  filter:
    request:
      enabled: true # 控制请求日志是否启用
      include-payload: false # 是否包含请求体
      include-headers: false # 是否包含请求头

# WebSocket配置
websocket:
  chat-service-uri: http://localhost:9085 # 聊天服务的URI
  multi-chat-service-uri: http://localhost:9086 # 多人聊天服务的URI
  allowed-origins: "*" # 允许的来源
