package com.gw.membership.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 会员套餐DTO
 */
@Data
@Schema(description = "会员套餐DTO")
public class MembershipPackageVO {
    private Long id;


    /**
     * 套餐名称
     */
    @Size(max = 50, message = "套餐名称长度不能超过50个字符")
    @Schema(description = "套餐名称")
    private String name;

    /**
     * 套餐描述
     */
    @Schema(description = "套餐描述")
    private String description;

    /**
     * 套餐价格(元)
     */
    @NotNull(message = "套餐价格不能为空")
    private BigDecimal price;

    /**
     * 折扣价格(元)
     */
    @Schema(description = "折扣价格(元)")
    private BigDecimal discountPrice;

    /**
     * 有效期(天)，0表示永久
     */
    @Schema(description = "有效期(天)，0表示永久")
    private Integer validDays;

    /**
     * 套餐类型: 1-包月, 2-包季, 3-包年, 4-终身
     */
    @Schema(description = "套餐类型: 1-包月, 2-包季, 3-包年, 4-终身")
    private Integer type = 1;

    /**
     * 套餐状态: 0-下架, 1-上架
     */
    @Schema(description = "套餐状态: 0-下架, 1-上架")
    private Integer status = 1;

    /**
     * 排序优先级
     */
    @Schema(description = "排序优先级")
    private Integer sortOrder = 0;

    /**
     * 是否推荐: 0-否, 1-是
     */
    @Schema(description = "是否推荐: 0-否, 1-是")
    private Integer isRecommended = 0;

    /**
     * 套餐图标URL
     */
    private String iconUrl;
    private Integer vipLevel;
    /**
     * 关联的权益ID列表
     */

    private List<BenefitCategoryVO> benefits;

}