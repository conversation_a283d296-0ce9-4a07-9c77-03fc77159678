package com.gw.membership.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户会员历史记录VO
 */
@Data
@Schema(description = "用户会员历史记录VO")
public class UserMembershipHistoryVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "用户ID")
    private String username;

    @Schema(description = "操作类型: 1-新开通, 2-续费, 3-赠送, 4-管理员操作, 5-邀请获得, 6-过期, 7-取消, 8-套餐变更")
    private Integer operationType;

    @Schema(description = "操作类型描述")
    private String operationTypeDesc;

    @Schema(description = "相关订单ID")
    private Long orderId;

    @Schema(description = "套餐ID")
    private Long packageId;

    @Schema(description = "套餐名称")
    private String packageName;

    @Schema(description = "变更前会员状态: 0-非会员, 1-会员")
    private Integer beforeStatus;

    @Schema(description = "变更后会员状态: 0-非会员, 1-会员")
    private Integer afterStatus;

    @Schema(description = "变更前到期时间")
    private LocalDateTime beforeExpireTime;

    @Schema(description = "变更后到期时间")
    private LocalDateTime afterExpireTime;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "操作来源: 1-用户购买, 2-系统自动, 3-管理员操作, 4-邀请码")
    private Integer source;

    @Schema(description = "操作来源描述")
    private String sourceDesc;

    @Schema(description = "邀请人用户名")
    private String inviteUsername;

    @Schema(description = "备注")
    private String remark;
} 