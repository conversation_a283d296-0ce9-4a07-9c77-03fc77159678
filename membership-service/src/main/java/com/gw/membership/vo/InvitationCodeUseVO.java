package com.gw.membership.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Schema(description = "邀请码验证结果")
@NoArgsConstructor
@AllArgsConstructor
public class InvitationCodeUseVO {
    @Schema(description = "邀请码")
    @NotBlank(message = "邀请码不能为空")
    private String code;
    private boolean valid;
}
