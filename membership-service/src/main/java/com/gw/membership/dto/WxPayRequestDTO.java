package com.gw.membership.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * 微信支付请求DTO
 */
@Data
@Schema(description = "微信支付请求参数")
public class WxPayRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    @Schema(description = "订单号", required = true)
    private String orderNo;

//    /**
//     * 微信小程序用户openId
//     */
//    @NotBlank(message = "openId不能为空")
//    private String openId;
} 