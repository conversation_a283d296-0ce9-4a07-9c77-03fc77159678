package com.gw.membership.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 会员积分操作DTO
 */
@Data
@Schema(description = "会员积分操作请求")
public class MemberPointsDTO {

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Schema(description = "用户名", required = true)
    private String username;

    /**
     * 积分数量
     */
    @NotNull(message = "积分数量不能为空")
    @Min(value = 1, message = "积分数量必须大于0")
    @Schema(description = "积分数量", required = true)
    private Integer points;

    /**
     * 操作原因
     */
    @Schema(description = "操作原因")
    private String reason;
}
