package com.gw.membership.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 会员订单创建DTO
 */
@Data
@Schema(description = "会员订单创建请求")
public class MembershipOrderDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 套餐ID
     */
    @NotNull(message = "套餐ID不能为空")
    @Schema(description = "套餐ID", required = true)
    private Long packageId;

    /**
     * 支付方式：1-微信支付，2-支付宝，3-余额支付
     */
    @NotNull(message = "支付方式不能为空")
    @Schema(description = "支付方式：1-微信支付，2-支付宝，3-余额支付", required = true)
    private Integer payMethod;

    /**
     * 优惠码
     */
    @Schema(description = "优惠码")
    private String couponCode;

    /**
     * 订单类型：1-新购，2-续费，3-升级（选填，默认为1）
     */
    @Schema(description = "订单类型：1-新购，2-续费，3-升级（选填，默认为1）")
    private Integer orderType;

    /**
     * 客户端类型: 1-小程序, 2-iOS, 3-Android
     */
    @Schema(description = "客户端类型: 1-小程序, 2-iOS, 3-Android")
    private Integer clientType = 1;

    /**
     * 订单备注（选填）
     */
    @Schema(description = "订单备注（选填）")
    private String remark;

    /**
     * 邀请码（选填）
     */
    @Schema(description = "邀请码（选填）")
    private String invitationCode;

    /**
     * 操作人ID（后台赠送/补录时使用）
     */
    private String operator;

    /**
     * 用户名（后台操作时需要指定）
     */
    @Schema(description = "后台操作的时候指定")
    private String username;
    @Schema(description = "后台操作的时候指定")
    @JsonIgnore
    private String sourceUsername;
}