package com.gw.membership.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员折扣DTO
 */
@Data
public class MembershipDiscountDTO {


    /**
     * 折扣名称
     */
    @Schema(description = "会员折扣名称")
    private String name;

    /**
     * 折扣类型: 1-固定金额, 2-百分比
     */
    @Schema(description = "会员折扣类型: 1-固定金额, 2-百分比")
    private Integer discountType;

    /**
     * 折扣值（固定金额或百分比）
     */
    @Schema(description = "会员折扣值（固定金额或百分比）")
    private BigDecimal discountValue;

    /**
     * 最低订单金额
     */
    @Schema(description = "最低订单金额")
    private BigDecimal minOrderAmount;

    /**
     * 折扣开始时间
     */
    @Schema(description = "折扣开始时间")
    private LocalDateTime startTime;

    /**
     * 折扣结束时间
     */
    @Schema(description = "折扣结束时间")
    private LocalDateTime endTime;

    /**
     * 折扣状态: 0-禁用, 1-启用
     */
    @Schema(description = "折扣状态: 0-禁用, 1-启用")
    private Integer status = 1;

    /**
     * 适用的套餐ID列表, 逗号分隔, 为空表示适用所有套餐
     */
    @Schema(description = "适用的套餐ID列表, 为空表示适用所有套餐")
    private List<Long> packageIds;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 