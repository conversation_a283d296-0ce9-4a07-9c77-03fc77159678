package com.gw.chat.dto;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gw.chat.entity.ChatMessage;
import com.gw.chat.entity.ConversationSession;
import com.gw.common.agent.vo.AgentBaseVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 聊天消息数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatMessageDto {

    /**
     * 消息ID
     */
    private String id;

    /**
     * 会话ID
     */
    private String sessionId;
    private String username;

    private Long agentId;
    private String sender;
    private String role;
    private String type;
    private String content;
    private String contentType;
    private String audioUrl;
    private String audioDuration;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 消息状态
     */
    private ChatMessage.MessageStatus status;
    private boolean reAnswer;
    private String lstQueryChatId;
    private String lstAnswerChatId;
    private AgentBaseVO agent;
    private ConversationSession session;
    private Long seqNum = 0L;
    /**
     * 从实体转换为DTO
     */
    public static ChatMessageDto fromEntity(ChatMessage entity) {
        return ChatMessageDto.builder()
                .id(entity.getId())
                .username(entity.getUsername())
                .sessionId(entity.getSessionId())
                .agentId(entity.getAgentId())
                .type(entity.getType())
                .seqNum(entity.getSeqNum())
                .role(entity.getRole())
                .contentType(entity.getContentType())
                .content(entity.getContent())
                .createdAt(entity.getCreatedAt())
                .status(entity.getStatus())
                .audioUrl(entity.getAudioUrl())
                .audioDuration(entity.getAudioDuration())
                .build();
    }

    /**
     * 转换为实体
     */
    public ChatMessage toEntity() {
        ChatMessage message = new ChatMessage();
        message.setId(this.id);
        message.setSessionId(this.sessionId);
        message.setAgentId(this.agentId);
        message.setContentType(this.contentType);
        message.setRole(this.role);
        message.setStatus(this.status);
        message.setContent(this.content);
        message.setCreatedAt(this.createdAt);
        message.setType(this.type);
        message.setAudioUrl(this.audioUrl);
        message.setAudioDuration(this.audioDuration);
        message.setUsername(this.username);
        return message;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}