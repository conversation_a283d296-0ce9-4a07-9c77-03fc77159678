package com.gw.chat.dto;

import com.gw.chat.vo.ChatContextVO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class SendChatMessageDTO {
    @Schema(description = "会话ID")
    @NotBlank(message = "会话ID不能为空")
    private String conversationId;

    @Schema(description = "机器人ID")
    @NotBlank(message = "机器人ID不能为空")
    private String botId;

    @Schema(description = "用户ID")
    @NotBlank(message = "用户ID不能为空")
    private String uid;

    @Schema(description = "聊天上下文消息列表")
    @NotNull(message = "消息列表不能为空")
    private List<ChatContextVO> message;

    @Schema(description = "自定义变量")
    private Map<String, String> customVariables;

    @Schema(description = "是否启用音频")
    private boolean audio = false;

    @Schema(description = "语音ID")
    private String voiceId;
}