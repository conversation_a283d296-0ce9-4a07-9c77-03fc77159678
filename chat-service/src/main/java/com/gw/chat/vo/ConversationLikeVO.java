package com.gw.chat.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "会话点赞数据")
public class ConversationLikeVO {

    @Schema(description = "点赞ID")
    private String id;

    @Schema(description = "点赞用户名")
    private String liker;

    @Schema(description = "点赞用户昵称")
    private String likerNickname;

    @Schema(description = "AI助手ID")
    private Long agentId;

    @Schema(description = "会话ID")
    private String conversationId;

    @Schema(description = "点赞状态")
    private Boolean liked;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
} 