package com.gw.chat.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "点赞统计数据")
public class LikeStatsVO {

    @Schema(description = "会话ID")
    private String conversationId;

    @Schema(description = "点赞数量")
    private Long likeCount;

    @Schema(description = "当前用户是否点赞")
    private Boolean userLiked;
} 