package com.gw.chat.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 智能体统计数据的VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "智能体统计数据响应")
public class AgentStatsVO {

    @Schema(description = "智能体ID")
    private Long agentId;

    @Schema(description = "智能体名称")
    private String agentName;

    @Schema(description = "会话总数")
    private long totalSessions;

    @Schema(description = "活跃用户数")
    private long activeUsers;

    private List<SessionStatsVO> sessionStats;
}