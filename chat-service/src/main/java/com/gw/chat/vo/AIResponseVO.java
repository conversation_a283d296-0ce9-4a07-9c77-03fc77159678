package com.gw.chat.vo;

import com.alibaba.fastjson2.JSONArray;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class AIResponseVO {
    private String status;
    private LocalDateTime completedAt;
    private LocalDateTime createdAt;
    private LocalDateTime failedAt;
    private List<ChatContextVO> contexts;
    private JSONArray followMsg;
    private String remoteContextId = null;
}
