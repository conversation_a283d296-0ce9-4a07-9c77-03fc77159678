package com.gw.chat.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SessionStatsVO {
    @Schema(description = "会话ID")
    private String sessionId;
    @Schema(description = "用户ID")
    private String username;
    @Schema(description = "用户昵称")
    private String nickname;
    @Schema(description = "最后一条消息")
    private String lastContent;
    @Schema(description = "最后一条消息时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastContentTime;
    @Schema(description = "评论数")
    private long commentCnt;
    @Schema(description = "点赞数")
    private long likeCnt;
}
