package com.gw.user.demo;

import lombok.extern.log4j.Log4j2;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 并发控制演示程序
 * 演示如何防止频繁的缓存更新操作
 */
@Log4j2
public class ConcurrencyControlDemo {
    
    // 缓存更新控制相关常量
    private static final long CACHE_UPDATE_INTERVAL = 5000; // 5秒间隔
    
    // 缓存更新状态控制
    private final AtomicBoolean isUpdating = new AtomicBoolean(false);
    private final AtomicLong lastUpdateTime = new AtomicLong(0);
    
    /**
     * 模拟缓存更新操作
     */
    private void updateCache() {
        try {
            log.info("开始更新缓存...");
            Thread.sleep(2000); // 模拟耗时操作
            log.info("缓存更新完成");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("缓存更新被中断", e);
        }
    }
    
    /**
     * 尝试异步更新缓存，带有冷却时间检查和并发控制
     */
    public void tryUpdateCacheAsync() {
        long currentTime = System.currentTimeMillis();
        long lastUpdate = lastUpdateTime.get();

        // 如果距离上次更新时间不足指定间隔，则跳过
        if (currentTime - lastUpdate < CACHE_UPDATE_INTERVAL) {
            log.info("距离上次更新时间不足指定间隔，跳过本次更新");
            return;
        }

        // 如果已经有任务在执行，则跳过
        if (!isUpdating.compareAndSet(false, true)) {
            log.info("缓存更新任务已经在执行，跳过本次更新");
            return;
        }

        log.info("启动异步缓存更新任务");
        
        // 使用异步任务执行缓存更新
        CompletableFuture.runAsync(() -> {
            try {
                updateCache();
                // 更新最后更新时间
                lastUpdateTime.set(System.currentTimeMillis());
            } catch (Exception e) {
                log.error("异步更新缓存失败", e);
            } finally {
                // 释放执行标志
                isUpdating.set(false);
            }
        }, ForkJoinPool.commonPool());
    }
    
    /**
     * 演示并发控制效果
     */
    public static void main(String[] args) throws InterruptedException {
        ConcurrencyControlDemo demo = new ConcurrencyControlDemo();
        
        log.info("=== 并发控制演示开始 ===");
        
        // 模拟多个并发请求
        for (int i = 1; i <= 10; i++) {
            final int requestId = i;
            new Thread(() -> {
                log.info("请求 {} 尝试更新缓存", requestId);
                demo.tryUpdateCacheAsync();
            }, "Request-" + i).start();
            
            // 短暂间隔
            Thread.sleep(100);
        }
        
        // 等待一段时间观察结果
        Thread.sleep(3000);
        
        log.info("=== 等待5秒后再次尝试 ===");
        Thread.sleep(5000);
        
        // 再次尝试更新
        for (int i = 11; i <= 15; i++) {
            final int requestId = i;
            new Thread(() -> {
                log.info("请求 {} 尝试更新缓存", requestId);
                demo.tryUpdateCacheAsync();
            }, "Request-" + i).start();
            
            Thread.sleep(100);
        }
        
        // 等待所有任务完成
        Thread.sleep(5000);
        log.info("=== 并发控制演示结束 ===");
    }
}
