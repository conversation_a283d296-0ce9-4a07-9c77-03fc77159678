package com.gw.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "菜单树形结构")
public class MenuVO {
    @Schema(description = "菜单ID")
    private Long id;

    @Schema(description = "菜单名称")
    private String label;

    @Schema(description = "权限标识")
    private String auth;

    @Schema(description = "是否禁用")
    private Boolean disabled;

    @Schema(description = "子菜单")
    private List<MenuVO> children;
} 