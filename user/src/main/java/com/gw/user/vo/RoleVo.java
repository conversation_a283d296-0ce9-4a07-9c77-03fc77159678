package com.gw.user.vo;

import com.gw.user.entity.RoleEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoleVo extends RoleBaseVo {
    @Schema(description = "授权的菜单")
    private List<String> roles;
    @Schema(description = "授权的按键")
    private List<String> authBtnlist;

    public RoleVo(RoleEntity role) {
        super(role);
        this.roles = role.getRoleMenus();
        this.authBtnlist = role.getBtns();
    }
}
