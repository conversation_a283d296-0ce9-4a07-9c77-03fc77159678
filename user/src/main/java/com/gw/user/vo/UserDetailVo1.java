package com.gw.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class UserDetailVo1 extends UserDetailVo {
    @Schema(description = "创建数")
    private int creatCnt = 0;
    @Schema(description = "点赞数")
    private int likeCnt = 0;
    @Schema(description = "评论数")
    private int commentCnt = 0;
    @Schema(description = "收藏数")
    private int favoriteCnt = 0;
}
