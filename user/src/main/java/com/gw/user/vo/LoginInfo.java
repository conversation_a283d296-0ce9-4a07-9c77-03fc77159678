package com.gw.user.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "登录返回的信息")
public class LoginInfo {
    @Schema(description = "token 后续请求用")
    private String accessToken;
    @Schema(description = "用户名")
    private String username;
    @Schema(description = "真实姓名")
    private String realName;
    private Long userId;
    @JsonIgnore
    private String refreshToken;
    @Schema(description = "超时时间 单位是秒")
    @JsonIgnore
    private long expiresTime;
    @Schema(description = "授权的菜单")
    private List<String> roles;
    @Schema(description = "授权的按键")
    private List<String> authBtnlist;
}
