package com.gw.user.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Schema(description = "用户查询条件")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAppQueryDto {
    @Schema(description = "模糊查询")
    private String queryValue = "";
    @JsonIgnore
    private Integer isShow = -1;
    @JsonIgnore
    private String roleCode;
}
