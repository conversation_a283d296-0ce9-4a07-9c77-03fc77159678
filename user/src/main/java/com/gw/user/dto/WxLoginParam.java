package com.gw.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "微信小程序一键登录参数")
public class WxLoginParam extends WxLoginBaseParam{
    @NotBlank(message = "code不能为空")
    @Schema(description = "微信登录code")
    private String code;
    public String toString() {
        return "WxLoginParam{" +
                "openIdCode='" + getOpenIdCode() + '\'' +
                ", code='" + code + '\'' +
                ", inviteCode='" + getInviteCode() + '\'' +
                ", encryptedData='" + getIv() + '\'' +
                ", iv='" + getIv() + '\'' +
                '}';
    }
}