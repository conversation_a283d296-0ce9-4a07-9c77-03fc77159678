package com.gw.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(description = "微信注册参数")
public class WxRegisterParam {

    @NotBlank(message = "手机号不能为空")
    @Schema(description = "手机号", example = "13800138000")
    private String phoneNumber;

    @Schema(description = "昵称", example = "微信用户")
    private String nickname;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatarUrl;

    @Schema(description = "性别", example = "1")
    private Integer gender;

    @Schema(description = "国家", example = "中国")
    private String country;

    @Schema(description = "省份", example = "广东")
    private String province;

    @Schema(description = "城市", example = "深圳")
    private String city;

    @Schema(description = "语言", example = "zh_CN")
    private String language;

    @Schema(description = "邀请码", example = "INVITE123")
    private String inviteCode;

    @Schema(description = "微信OpenID", example = "openid123456")
    private String openId;

    @Schema(description = "微信UnionID", example = "unionid123456")
    private String unionId;
}