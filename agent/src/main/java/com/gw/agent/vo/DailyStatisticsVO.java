package com.gw.agent.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 按天统计数据VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "按天统计数据")
public class DailyStatisticsVO implements Serializable {

    @Schema(description = "日期，格式：yyyy-MM-dd")
    private String date;

    @Schema(description = "新增注册用户数")
    private Integer newUserCount;

    @Schema(description = "活跃用户数")
    private Integer activeUserCount;
} 