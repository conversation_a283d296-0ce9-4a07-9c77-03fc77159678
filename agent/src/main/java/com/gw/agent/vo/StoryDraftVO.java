package com.gw.agent.vo;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gw.agent.entity.StoryDraftEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Schema(description = "群聊草稿箱")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StoryDraftVO {
    @Schema(description = "草稿箱ID 如果是第一次提交是0")
    private Long id;
    @Schema(description = "草稿箱名称")
    private String name;
    @Schema(description = "草稿箱内容")
    private JSONObject content;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    public StoryDraftVO(StoryDraftEntity entity) {
        this.id = entity.getId();
        this.name = entity.getName();
        this.updateTime = entity.getUpdateTime();
        this.content = JSONObject.parseObject(entity.getContent());
    }
}
