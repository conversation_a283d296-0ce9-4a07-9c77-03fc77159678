package com.gw.agent.vo;

import com.gw.agent.entity.AgentTypeEntity;
import com.gw.agent.entity.UserAgentPreferenceEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@Schema(description = "用户喜好设置VO")
public class UserPreferenceVO {

    /**
     * 喜好的智能体性别：1-男，2-女，3-男女都可以
     */
    @Schema(description = "喜好的智能体性别：1-男，2-女，3-男女都可以")
    private Integer preferredGender;

    /**
     * 喜好的智能体类型ID列表，多选
     */
    @Schema(description = "喜好的智能体类型ID列表，多选")
    private List<Long> preferredAgentTypes = new ArrayList<>();

    /**
     * 喜好的智能体类型实体列表
     */
    @Schema(description = "喜好的智能体类型实体列表")
    private List<AgentTypeVO> preferredAgentTypeVOs = new ArrayList<>();

    public UserPreferenceVO(UserAgentPreferenceEntity entity) {
        if (entity != null) {
            BeanUtils.copyProperties(entity, this);
        }
    }

    public UserPreferenceVO(UserAgentPreferenceEntity entity, List<AgentTypeEntity> typeEntities) {
        this(entity);
        if (typeEntities != null && !typeEntities.isEmpty()) {
            for (AgentTypeEntity typeEntity : typeEntities) {
                preferredAgentTypeVOs.add(new AgentTypeVO(typeEntity));
            }
        }
    }
} 